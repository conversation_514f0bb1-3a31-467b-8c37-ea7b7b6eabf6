/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminHeader from '@/components/AdminHeader';
import Button from '@/components/ui/Button';
import {
  BarChart3,
  Users,
  FileText,
  DollarSign,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  Car,
  Shield
} from 'lucide-react';

interface AdminStats {
  totalSubmissions: number;
  totalUsers: number;
  totalRevenue: number;
  pendingForms: number;
  approvedForms: number;
  rejectedForms: number;
  recentActivity: any[];
}

// Animated Counter Component
const AnimatedCounter: React.FC<{ end: number; duration?: number; delay?: number }> = ({
  end,
  duration = 2000,
  delay = 0
}) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasStarted(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (!hasStarted) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, hasStarted]);

  return <span>{count.toLocaleString()}</span>;
};

// Real-time Clock Component
const RealTimeClock: React.FC = () => {
  const [time, setTime] = useState(new Date());

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  return (
    <span className="text-prp-gold text-sm font-medium">
      Last updated: {time.toLocaleTimeString()}
    </span>
  );
};

export default function AdminDashboardPage() {
  const router = useRouter();
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [adminData, setAdminData] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check admin authentication
    const token = localStorage.getItem('admin_token');
    const adminInfo = localStorage.getItem('admin_data');
    
    if (!token || !adminInfo) {
      router.push('/admin/login');
      return;
    }

    try {
      const admin = JSON.parse(adminInfo);
      if (admin.role !== 'admin') {
        router.push('/admin/login');
        return;
      }
      setAdminData(admin);
    } catch (error) {
      router.push('/admin/login');
      return;
    }

    // Load admin stats
    loadAdminStats();
  }, [router]);

  // Trigger animations after data loads
  useEffect(() => {
    if (stats && !isLoading) {
      setIsVisible(true);
    }
  }, [stats, isLoading]);

  const loadAdminStats = async () => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to load admin stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_data');
    router.push('/admin/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-prp-gold mx-auto mb-4"></div>
          <p className="text-prp-silver animate-pulse">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-prp-gold/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <AdminHeader onLogout={handleLogout} currentPage="dashboard" />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        {/* Welcome Section */}
        <div className={`mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center group">
                <Shield className="w-10 h-10 mr-3 text-prp-gold transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
                <span className="bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
                  Admin Dashboard
                </span>
              </h1>
              <p className="text-prp-silver text-lg">
                Welcome back, <span className="text-prp-gold font-semibold">{adminData?.name}</span>. Here is your system overview.
              </p>
            </div>
            <div className="hidden sm:block">
              <div className="bg-prp-dark/60 backdrop-blur-sm border border-prp-gold/30 rounded-xl px-4 py-2 shadow-lg hover:shadow-prp-gold/20 transition-all duration-300 hover:scale-105">
                <RealTimeClock />
              </div>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Submissions */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-blue-400/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '100ms' }}>
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500/20 rounded-lg group-hover:bg-blue-500/30 transition-all duration-300 group-hover:scale-110">
                <FileText className="w-6 h-6 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
              </div>
              <span className="text-2xl font-bold text-prp-light group-hover:text-blue-300 transition-colors duration-300">
                <AnimatedCounter end={stats?.totalSubmissions || 0} delay={200} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Total Submissions</h3>
            <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">All form submissions</p>
          </div>

          {/* Total Users */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-green-400/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '200ms' }}>
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-500/20 rounded-lg group-hover:bg-green-500/30 transition-all duration-300 group-hover:scale-110">
                <Users className="w-6 h-6 text-green-400 group-hover:text-green-300 transition-colors duration-300" />
              </div>
              <span className="text-2xl font-bold text-prp-light group-hover:text-green-300 transition-colors duration-300">
                <AnimatedCounter end={stats?.totalUsers || 0} delay={400} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Total Users</h3>
            <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">Registered salesmen</p>
          </div>

          {/* Total Revenue */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-prp-gold/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '300ms' }}>
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-prp-gold/20 rounded-lg group-hover:bg-prp-gold/30 transition-all duration-300 group-hover:scale-110">
                <DollarSign className="w-6 h-6 text-prp-gold group-hover:text-yellow-300 transition-colors duration-300" />
              </div>
              <span className="text-2xl font-bold text-prp-light group-hover:text-prp-gold transition-colors duration-300">
                $<AnimatedCounter end={stats?.totalRevenue || 0} delay={600} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Total Revenue</h3>
            <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">PRP sales</p>
          </div>

          {/* Pending Forms */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-yellow-400/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '400ms' }}>
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-yellow-500/20 rounded-lg group-hover:bg-yellow-500/30 transition-all duration-300 group-hover:scale-110">
                <Clock className="w-6 h-6 text-yellow-400 group-hover:text-yellow-300 transition-colors duration-300" />
              </div>
              <span className="text-2xl font-bold text-prp-light group-hover:text-yellow-300 transition-colors duration-300">
                <AnimatedCounter end={stats?.pendingForms || 0} delay={800} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Pending Forms</h3>
            <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">Awaiting review</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Form Status Overview */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-[1.02] group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '500ms' }}>
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center group-hover:text-prp-gold transition-colors duration-300">
              <BarChart3 className="w-6 h-6 mr-2 text-prp-gold group-hover:scale-110 transition-transform duration-300" />
              Form Status Overview
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg hover:bg-yellow-500/20 hover:border-yellow-500/50 transition-all duration-300 hover:scale-105 group/item">
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-yellow-400 mr-3 group-hover/item:scale-110 transition-transform duration-300" />
                  <span className="text-prp-light font-medium group-hover/item:text-yellow-300 transition-colors duration-300">Pending</span>
                </div>
                <span className="text-2xl font-bold text-yellow-400 group-hover/item:text-yellow-300 transition-colors duration-300">
                  <AnimatedCounter end={stats?.pendingForms || 0} delay={1000} />
                </span>
              </div>
              <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/30 rounded-lg hover:bg-green-500/20 hover:border-green-500/50 transition-all duration-300 hover:scale-105 group/item">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-400 mr-3 group-hover/item:scale-110 transition-transform duration-300" />
                  <span className="text-prp-light font-medium group-hover/item:text-green-300 transition-colors duration-300">Approved</span>
                </div>
                <span className="text-2xl font-bold text-green-400 group-hover/item:text-green-300 transition-colors duration-300">
                  <AnimatedCounter end={stats?.approvedForms || 0} delay={1200} />
                </span>
              </div>
              <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/30 rounded-lg hover:bg-red-500/20 hover:border-red-500/50 transition-all duration-300 hover:scale-105 group/item">
                <div className="flex items-center">
                  <XCircle className="w-5 h-5 text-red-400 mr-3 group-hover/item:scale-110 transition-transform duration-300" />
                  <span className="text-prp-light font-medium group-hover/item:text-red-300 transition-colors duration-300">Rejected</span>
                </div>
                <span className="text-2xl font-bold text-red-400 group-hover/item:text-red-300 transition-colors duration-300">
                  <AnimatedCounter end={stats?.rejectedForms || 0} delay={1400} />
                </span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-[1.02] group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '600ms' }}>
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center group-hover:text-prp-gold transition-colors duration-300">
              <TrendingUp className="w-6 h-6 mr-2 text-prp-gold group-hover:scale-110 transition-transform duration-300" />
              Quick Actions
            </h3>
            <div className="space-y-4">
              <Button
                onClick={() => router.push('/admin/forms')}
                variant="primary"
                className="w-full text-lg py-4 transform transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-prp-gold/30"
              >
                Manage Form Submissions
              </Button>
              <Button
                onClick={() => router.push('/admin/analytics')}
                variant="outline"
                className="w-full text-lg py-4 transform transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-prp-gold/20"
              >
                View Analytics
              </Button>
              <Button
                onClick={() => router.push('/admin/users')}
                variant="ghost"
                className="w-full text-lg py-4 transform transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-prp-silver/20"
              >
                Manage Users
              </Button>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
