import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import FormSubmission from '@/models/FormSubmission';
import User from '@/models/User';
import { verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    // Verify token and check if user is admin
    const payload = verifyToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '6months';

    // Calculate date range
    let startDate = new Date();
    switch (timeRange) {
      case '1month':
        startDate.setMonth(startDate.getMonth() - 1);
        break;
      case '3months':
        startDate.setMonth(startDate.getMonth() - 3);
        break;
      case '6months':
        startDate.setMonth(startDate.getMonth() - 6);
        break;
      case '1year':
        startDate.setFullYear(startDate.getFullYear() - 1);
        break;
      case 'all':
        startDate = new Date('2020-01-01'); // Far back date
        break;
      default:
        startDate.setMonth(startDate.getMonth() - 6);
    }

    // Get submissions within date range
    const submissions = await FormSubmission.find({
      createdAt: { $gte: startDate }
    })
      .populate('userId', 'name email dealership')
      .sort({ createdAt: -1 })
      .lean();

    // Get all users
    const users = await User.find({ role: 'salesman' }).lean();

    // Calculate basic statistics
    const totalSubmissions = submissions.length;
    const totalUsers = users.length;
    const totalRevenue = submissions.reduce((sum, sub) => sum + sub.prpSales, 0);
    const totalCarValue = submissions.reduce((sum, sub) => sum + sub.totalCarValue, 0);
    const averageCarValue = totalSubmissions > 0 ? totalCarValue / totalSubmissions : 0;
    const averagePrpSales = totalSubmissions > 0 ? totalRevenue / totalSubmissions : 0;

    // Status breakdown
    const statusBreakdown = {
      pending: submissions.filter(sub => sub.status === 'pending').length,
      approved: submissions.filter(sub => sub.status === 'approved').length,
      rejected: submissions.filter(sub => sub.status === 'rejected').length,
    };

    // Monthly data
    const monthlyData = [];
    const now = new Date();
    const monthsToShow = timeRange === '1month' ? 4 : timeRange === '3months' ? 6 : timeRange === '1year' ? 12 : 6;
    
    for (let i = monthsToShow - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      const monthSubmissions = submissions.filter(sub => {
        const subDate = new Date(sub.createdAt);
        return subDate >= date && subDate < nextDate;
      });

      monthlyData.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        submissions: monthSubmissions.length,
        revenue: monthSubmissions.reduce((sum, sub) => sum + sub.prpSales, 0)
      });
    }

    // Dealership breakdown
    const dealershipMap = new Map();
    submissions.forEach(sub => {
      const dealership = sub.userId?.dealership || 'Unknown';
      if (!dealershipMap.has(dealership)) {
        dealershipMap.set(dealership, { submissions: 0, revenue: 0 });
      }
      const data = dealershipMap.get(dealership);
      data.submissions += 1;
      data.revenue += sub.prpSales;
    });

    const dealershipBreakdown = Array.from(dealershipMap.entries()).map(([dealership, data]) => ({
      dealership,
      submissions: data.submissions,
      revenue: data.revenue
    })).sort((a, b) => b.revenue - a.revenue);

    // Salesman performance
    const salesmanMap = new Map();
    submissions.forEach(sub => {
      const salesmanId = sub.userId?._id?.toString() || 'unknown';
      if (!salesmanMap.has(salesmanId)) {
        salesmanMap.set(salesmanId, {
          name: sub.userId?.name || 'Unknown',
          dealership: sub.userId?.dealership || 'Unknown',
          submissions: 0,
          revenue: 0
        });
      }
      const data = salesmanMap.get(salesmanId);
      data.submissions += 1;
      data.revenue += sub.prpSales;
    });

    const salesmanPerformance = Array.from(salesmanMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10); // Top 10 performers

    const analytics = {
      totalRevenue,
      totalSubmissions,
      totalUsers,
      averageCarValue,
      averagePrpSales,
      statusBreakdown,
      monthlyData,
      dealershipBreakdown,
      salesmanPerformance
    };

    return NextResponse.json(analytics);

  } catch (error) {
    console.error('Admin analytics API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
