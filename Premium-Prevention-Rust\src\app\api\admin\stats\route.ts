import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import FormSubmission from '@/models/FormSubmission';
import User from '@/models/User';
import { verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    // Verify token and check if user is admin
    const payload = verifyToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    // Get all form submissions
    const submissions = await FormSubmission.find({})
      .populate('userId', 'name email dealership')
      .sort({ createdAt: -1 })
      .lean();

    // Get all users
    const users = await User.find({ role: 'salesman' }).lean();

    // Calculate statistics
    const totalSubmissions = submissions.length;
    const totalUsers = users.length;
    const totalRevenue = submissions.reduce((sum, sub) => sum + sub.prpSales, 0);

    // Status breakdown
    const pendingForms = submissions.filter(sub => sub.status === 'pending').length;
    const approvedForms = submissions.filter(sub => sub.status === 'approved').length;
    const rejectedForms = submissions.filter(sub => sub.status === 'rejected').length;

    // Monthly data for the last 6 months
    const monthlyData = [];
    const now = new Date();
    for (let i = 5; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      const monthSubmissions = submissions.filter(sub => {
        const subDate = new Date(sub.createdAt);
        return subDate >= date && subDate < nextDate;
      });

      monthlyData.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        submissions: monthSubmissions.length,
        revenue: monthSubmissions.reduce((sum, sub) => sum + sub.prpSales, 0)
      });
    }

    // Recent activity (last 10 submissions)
    const recentActivity = submissions.slice(0, 10).map(sub => ({
      id: sub._id,
      vehicleName: sub.vehicleName,
      salesmanName: sub.userId?.name || 'Unknown',
      status: sub.status,
      prpSales: sub.prpSales,
      createdAt: sub.createdAt
    }));

    const stats = {
      totalSubmissions,
      totalUsers,
      totalRevenue,
      pendingForms,
      approvedForms,
      rejectedForms,
      monthlyData,
      recentActivity
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Admin stats API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
