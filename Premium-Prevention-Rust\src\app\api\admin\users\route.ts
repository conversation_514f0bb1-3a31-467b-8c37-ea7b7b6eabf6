import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import FormSubmission from '@/models/FormSubmission';
import { verifyAdminToken } from '@/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Verify admin authentication
    const authResult = verifyAdminToken(request);
    if (!authResult.isValid) {
      return NextResponse.json(
        { error: authResult.error },
        { status: 401 }
      );
    }

    // Get all users (excluding admins and passwords)
    const users = await User.find({ role: 'salesman' })
      .select('-password -__v')
      .sort({ createdAt: -1 })
      .lean();

    // Get submission counts and revenue for each user
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        const submissions = await FormSubmission.find({ userId: user._id }).lean();
        const submissionCount = submissions.length;
        const totalRevenue = submissions.reduce((sum, sub) => sum + sub.prpSales, 0);

        return {
          id: user._id,
          name: user.name,
          email: user.email,
          phone: user.phone,
          dealership: user.dealership,
          role: user.role,
          isActive: user.isActive,
          createdAt: user.createdAt,
          submissionCount,
          totalRevenue
        };
      })
    );

    return NextResponse.json({
      users: usersWithStats,
      total: usersWithStats.length
    });

  } catch (error) {
    console.error('Admin users API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
