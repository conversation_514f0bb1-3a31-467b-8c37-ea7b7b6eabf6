import { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';

export interface AdminAuthResult {
  isValid: boolean;
  adminId?: string;
  error?: string;
}

export function verifyAdminToken(request: NextRequest): AdminAuthResult {
  try {
    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return {
        isValid: false,
        error: 'Unauthorized - No token provided'
      };
    }

    // Verify token
    const payload = verifyToken(token);
    if (!payload) {
      return {
        isValid: false,
        error: 'Unauthorized - Invalid token'
      };
    }

    // Check if user is admin
    if (payload.role !== 'admin') {
      return {
        isValid: false,
        error: 'Unauthorized - Admin access required'
      };
    }

    return {
      isValid: true,
      adminId: payload.userId
    };

  } catch (error) {
    return {
      isValid: false,
      error: 'Unauthorized - Token verification failed'
    };
  }
}

export function createAdminAuthResponse(error: string, status: number = 401) {
  return Response.json({ error }, { status });
}
