import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyPassword, generateToken } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { email, password } = body;

    // Validation
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Allow authorized admin emails
    const authorizedAdminEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (!authorizedAdminEmails.includes(email.toLowerCase())) {
      return NextResponse.json(
        { error: 'Access denied. Invalid admin credentials.' },
        { status: 401 }
      );
    }

    // Find admin user by email
    const admin = await User.findOne({
      email: email.toLowerCase(),
      role: 'admin'
    });

    if (!admin) {
      return NextResponse.json(
        { error: 'Access denied. Invalid admin credentials.' },
        { status: 401 }
      );
    }

    // Check if admin is active
    if (!admin.isActive) {
      return NextResponse.json(
        { error: 'Admin account is deactivated.' },
        { status: 401 }
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, admin.password);
    if (!isPasswordValid) {
      return NextResponse.json(
        { error: 'Access denied. Invalid admin credentials.' },
        { status: 401 }
      );
    }

    // Generate JWT token for admin
    const token = generateToken({
      userId: admin._id.toString(),
      email: admin.email,
      role: admin.role
    });

    // Return admin data (without password)
    const adminData = {
      id: admin._id,
      name: admin.name,
      email: admin.email,
      phone: admin.phone,
      dealership: admin.dealership,
      role: admin.role,
      isActive: admin.isActive,
      createdAt: admin.createdAt
    };

    const response = NextResponse.json(
      {
        message: 'Admin login successful',
        admin: adminData,
        token: token,
        success: true
      },
      { status: 200 }
    );

    // Set admin token as HTTP-only cookie
    response.cookies.set('admin_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 30 * 24 * 60 * 60 // 30 days
    });

    return response;

  } catch (error) {
    console.error('Admin login error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
