'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Logo from './Logo';
import { LogOut, User, Menu } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useProfileImage } from '@/hooks/useProfileImage';

interface HeaderProps {
  onLogout?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onLogout }) => {
  const router = useRouter();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, logout } = useAuth();
  const { profileImage, loadProfileImage } = useProfileImage();

  useEffect(() => {
    if (user) {
      loadProfileImage();
    }
  }, [user, loadProfileImage]);

  const handleLogout = async () => {
    if (onLogout) {
      onLogout();
    } else {
      await logout();
    }
  };

  return (
    <header className="bg-prp-dark/90 backdrop-blur-sm border-b border-prp-silver/30 sticky top-0 z-50 shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Logo size="sm" showText={true} />
          </div>

          {/* Desktop Navigation */}
          {user && (
            <div className="hidden md:flex items-center space-x-6">
              <nav className="flex space-x-6">
                <button
                  onClick={() => router.push('/dashboard')}
                  className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium"
                >
                  Dashboard
                </button>
                <button
                  onClick={() => router.push('/forms')}
                  className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium"
                >
                  Forms
                </button>
                <button
                  onClick={() => router.push('/stats')}
                  className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium"
                >
                  Stats
                </button>
                  <button
                  onClick={() => {
                    if (window.location.pathname === '/dashboard') {
                      document.getElementById('faq-section')?.scrollIntoView({ behavior: 'smooth' });
                    } else {
                      router.push('/dashboard#faq-section');
                    }
                  }}
                  className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium"
                >
                  FAQ
                </button>
              </nav>

              {/* User Menu */}
              <div className="flex items-center space-x-4">
                <div
                  className="flex items-center space-x-3 text-prp-light cursor-pointer hover:text-prp-gold transition-colors duration-200"
                  onClick={() => router.push('/profile')}
                >
                  {profileImage ? (
                    <img
                      src={profileImage}
                      alt="Profile"
                      className="w-8 h-8 rounded-full object-cover border-2 border-prp-gold/30"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-prp-gold/20 flex items-center justify-center">
                      <User className="w-4 h-4 text-prp-gold" />
                    </div>
                  )}
                  <span className="text-sm font-medium hover:text-prp-gold transition-colors duration-200">{user.name}</span>
                  <span className="text-xs text-prp-silver bg-prp-gold/20 px-2 py-1 rounded-full">
                    {user.role}
                  </span>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 text-prp-light hover:text-prp-gold transition-colors duration-200"
                  title="Logout"
                >
                  <LogOut className="w-4 h-4" />
                  <span className="text-sm">Logout</span>
                </button>
              </div>
            </div>
          )}

          {/* Mobile menu button */}
          {user && (
            <div className="md:hidden">
              <button
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="text-prp-light hover:text-prp-gold transition-colors duration-200"
              >
                <Menu className="w-6 h-6" />
              </button>
            </div>
          )}

          {/* Login/Signup buttons for non-authenticated users */}
          {!user && (
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.push('/login')}
                className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium"
              >
                Login
              </button>
              <button
                onClick={() => router.push('/signup')}
                className="bg-prp-gold hover:bg-prp-gold/80 text-prp-dark px-4 py-2 rounded-lg font-medium transition-colors duration-200"
              >
                Sign Up
              </button>
            </div>
          )}
        </div>

        {/* Mobile Navigation Menu */}
        {user && isMenuOpen && (
          <div className="md:hidden border-t border-prp-silver/20 py-4">
            <nav className="flex flex-col space-y-3">
              <button
                onClick={() => {
                  router.push('/dashboard');
                  setIsMenuOpen(false);
                }}
                className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium text-left"
              >
                Dashboard
              </button>
              <button
                onClick={() => {
                  router.push('/forms');
                  setIsMenuOpen(false);
                }}
                className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium text-left"
              >
                Forms
              </button>
              <button
                onClick={() => {
                  router.push('/stats');
                  setIsMenuOpen(false);
                }}
                className="text-prp-light hover:text-prp-gold transition-colors duration-200 font-medium text-left"
              >
                Statstic
              </button>
              <div className="border-t border-prp-silver/20 pt-3 mt-3">
                <div
                  className="flex items-center space-x-3 text-prp-light mb-3 cursor-pointer hover:text-prp-gold transition-colors duration-200"
                  onClick={() => {
                    router.push('/profile');
                    setIsMenuOpen(false);
                  }}
                >
                  {profileImage ? (
                    <img
                      src={profileImage}
                      alt="Profile"
                      className="w-6 h-6 rounded-full object-cover border-2 border-prp-gold/30"
                    />
                  ) : (
                    <div className="w-6 h-6 rounded-full bg-prp-gold/20 flex items-center justify-center">
                      <User className="w-3 h-3 text-prp-gold" />
                    </div>
                  )}
                  <span className="text-sm font-medium">{user.name}</span>
                  <span className="text-xs text-prp-silver bg-prp-gold/20 px-2 py-1 rounded-full">
                    {user.role}
                  </span>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 text-prp-light hover:text-prp-gold transition-colors duration-200"
                >
                  <LogOut className="w-4 h-4" />
                  <span className="text-sm">Logout</span>
                </button>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
