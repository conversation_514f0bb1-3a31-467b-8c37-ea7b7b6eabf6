import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { addToBlacklist } from '@/lib/tokenBlacklist';

export async function POST(request: NextRequest) {
  try {
    // Get token from Authorization header to blacklist it
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    // Add token to blacklist if provided
    if (token) {
      addToBlacklist(token);
    }

    // Remove token from .env file
    const envPath = path.join(process.cwd(), '.env.local');
    let envContent = '';

    try {
      envContent = fs.readFileSync(envPath, 'utf8');
    } catch (error) {
      // File doesn't exist, continue with cookie clearing
    }

    if (envContent) {
      // Remove USER_TOKEN line
      const lines = envContent.split('\n').filter(line => !line.startsWith('USER_TOKEN='));

      // Write back to file
      fs.writeFileSync(envPath, lines.join('\n'));
    }

    const response = NextResponse.json(
      {
        message: 'Logout successful',
        success: true,
        redirectTo: '/' // Indicate where client should redirect
      },
      { status: 200 }
    );

    // Clear all possible auth cookies with different names
    const cookieOptions = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: 0, // Expire immediately
      path: '/' // Ensure cookie is cleared for entire domain
    };

    // Clear multiple possible cookie names
    response.cookies.set('auth_token', '', cookieOptions);
    response.cookies.set('token', '', cookieOptions);
    response.cookies.set('authToken', '', cookieOptions);
    response.cookies.set('user_token', '', cookieOptions);

    // Also set cookies to be deleted by setting them to expired
    response.cookies.set('auth_token', '', {
      ...cookieOptions,
      expires: new Date(0)
    });

    return response;

  } catch (error) {
    console.error('Logout error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        success: false
      },
      { status: 500 }
    );
  }
}
