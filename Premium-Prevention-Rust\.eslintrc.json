{"extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks"], "root": true, "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn"}, "settings": {"react": {"version": "detect"}}}