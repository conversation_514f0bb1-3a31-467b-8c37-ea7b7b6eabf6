import mongoose, { Document, Schema } from 'mongoose';

export interface IFormSubmission extends Document {
  userId: mongoose.Types.ObjectId;
  vehicleName: string;
  vehicleId: string;
  pictures: string[];
  timestamp: Date;
  date: Date;
  totalCarValue: number;
  prpSales: number; // Commission amount
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  vehicleModel?: string;
  vehicleYear?: number;
  vehicleColor?: string;
  mileage?: number;
  condition?: string;
  notes?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt: Date;
}

const FormSubmissionSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  vehicleName: {
    type: String,
    required: [true, 'Vehicle name is required'],
    trim: true,
    maxlength: [200, 'Vehicle name cannot be more than 200 characters']
  },
  vehicleId: {
    type: String,
    required: [true, 'Vehicle ID is required'],
    trim: true,
    maxlength: [50, 'Vehicle ID cannot be more than 50 characters']
  },
  pictures: [{
    type: String,
    trim: true
  }],
  timestamp: {
    type: Date,
    default: Date.now
  },
  date: {
    type: Date,
    required: [true, 'Date is required']
  },
  totalCarValue: {
    type: Number,
    required: [true, 'Total car value is required'],
    min: [0, 'Total car value must be positive']
  },
  prpSales: {
    type: Number,
    required: [true, 'PRP sales value is required'],
    min: [0, 'PRP sales must be positive']
  },
  customerName: {
    type: String,
    trim: true,
    maxlength: [100, 'Customer name cannot be more than 100 characters']
  },
  customerPhone: {
    type: String,
    trim: true,
    maxlength: [20, 'Phone number cannot be more than 20 characters']
  },
  customerEmail: {
    type: String,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  vehicleModel: {
    type: String,
    trim: true,
    maxlength: [100, 'Vehicle model cannot be more than 100 characters']
  },
  vehicleYear: {
    type: Number,
    min: [1900, 'Vehicle year must be after 1900'],
    max: [new Date().getFullYear() + 1, 'Vehicle year cannot be in the future']
  },
  vehicleColor: {
    type: String,
    trim: true,
    maxlength: [50, 'Vehicle color cannot be more than 50 characters']
  },
  mileage: {
    type: Number,
    min: [0, 'Mileage must be positive']
  },
  condition: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'poor'],
    default: 'good'
  },
  notes: {
    type: String,
    trim: true,
    maxlength: [1000, 'Notes cannot be more than 1000 characters']
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  }
}, {
  timestamps: true
});

// Indexes for faster queries
FormSubmissionSchema.index({ userId: 1 });
FormSubmissionSchema.index({ status: 1 });
FormSubmissionSchema.index({ createdAt: -1 });
FormSubmissionSchema.index({ vehicleId: 1 });

export default mongoose.models.FormSubmission || mongoose.model<IFormSubmission>('FormSubmission', FormSubmissionSchema);
