'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import Header from '@/components/Header';
import Button from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import Input from '@/components/ui/Input';
import { Upload, X, Car, DollarSign, Calendar, Camera } from 'lucide-react';
import { calculatePRPSales, formatCurrency } from '@/lib/utils';
import Swal from 'sweetalert2';

const formSchema = z.object({
  vehicleName: z.string().min(2, 'Vehicle name must be at least 2 characters').max(200, 'Vehicle name cannot exceed 200 characters'),
  vehicleId: z.string().min(1, 'Vehicle ID is required').max(50, 'Vehicle ID cannot exceed 50 characters'),
  totalCarValue: z.number().min(1, 'Total car value must be greater than 0'),
  customerName: z.string().optional(),
  customerPhone: z.string().optional(),
  customerEmail: z.string().email('Please enter a valid email').optional().or(z.literal('')),
  vehicleModel: z.string().optional(),
  vehicleYear: z.number().min(1900, 'Year must be after 1900').max(new Date().getFullYear() + 1, 'Year cannot be in the future').optional(),
  vehicleColor: z.string().optional(),
  mileage: z.number().min(0, 'Mileage must be positive').optional(),
  condition: z.enum(['excellent', 'good', 'fair', 'poor']).optional(),
  notes: z.string().max(1000, 'Notes cannot exceed 1000 characters').optional(),
  date: z.string().min(1, 'Date is required'),
});

type FormData = z.infer<typeof formSchema>;

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
}

export default function FormsPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [pictures, setPictures] = useState<File[]>([]);
  const [prpSales, setPrpSales] = useState(0);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors },
  } = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: new Date().toISOString().split('T')[0],
      condition: 'good',
    },
  });

  const totalCarValue = watch('totalCarValue');


useEffect(() => {
  //total prp sales
  if (totalCarValue && totalCarValue > 0) {
    let calculated = 0;

    if (totalCarValue <= 50000) {
      calculated = 2500;
    } else {
      calculated = calculatePRPSales(totalCarValue);
    }

    setPrpSales(calculated);
  } else {
    setPrpSales(0);
  }
}, [totalCarValue]);



  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      const isValidType = file.type.startsWith('image/');
      const isValidSize = file.size <= 5 * 1024 * 1024; // 5MB limit
      return isValidType && isValidSize;
    });

    if (validFiles.length !== files.length) {
      setError('Some files were rejected. Please upload only images under 5MB.');
    }

    setPictures(prev => [...prev, ...validFiles].slice(0, 10)); // Max 10 images
  };

  const removePicture = (index: number) => {
    setPictures(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: FormData) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      const formData = new FormData();
      
      // Add form fields
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && value !== '') {
          formData.append(key, value.toString());
        }
      });

      // Add calculated PRP sales
      formData.append('prpSales', prpSales.toString());

      // Add pictures
      pictures.forEach((file, index) => {
        formData.append(`picture_${index}`, file);
      });

      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/forms', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include', // Include cookies in the request
        body: formData,
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Form submission failed');
      }

      // Show SweetAlert2 success popup
      await Swal.fire({
        title: 'Success!',
        text: 'Form submitted successfully! Ready for another submission.',
        icon: 'success',
        confirmButtonText: 'Great!',
        confirmButtonColor: '#10b981',
        background: '#1f2937',
        color: '#f3f4f6',
        showConfirmButton: true,
        timer: 3000,
        timerProgressBar: true,
        customClass: {
          popup: 'border border-green-500/30 shadow-2xl',
          title: 'text-green-400 font-bold',
          htmlContainer: 'text-gray-300',
          confirmButton: 'bg-green-500 hover:bg-green-600 transition-colors duration-200'
        }
      });

      // Reset form fields using react-hook-form's reset function
      reset();

      // Reset additional state
      setPictures([]);
      setPrpSales(0);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred during form submission';

      // Show SweetAlert2 error popup
      await Swal.fire({
        title: 'Error!',
        text: errorMessage,
        icon: 'error',
        confirmButtonText: 'Try Again',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
        customClass: {
          popup: 'border border-red-500/30 shadow-2xl',
          title: 'text-red-400 font-bold',
          htmlContainer: 'text-gray-300',
          confirmButton: 'bg-red-500 hover:bg-red-600 transition-colors duration-200'
        }
      });

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
      <Header />

      <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-prp-light mb-2 flex items-center">
            <Car className="w-8 h-8 mr-3 text-prp-gold" />
            Vehicle Form Submission
          </h1>
          <p className="text-prp-silver">
            Submit vehicle information for Premium Rust Prevention service.
          </p>
        </div>

        <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-2xl p-8 shadow-2xl">
          {error && (
            <div className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
              <p className="text-red-400 text-sm font-medium">{error}</p>
            </div>
          )}



          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            {/* Vehicle Information */}
            <div>
              <h2 className="text-xl font-bold text-prp-light mb-4 flex items-center">
                <Car className="w-5 h-5 mr-2 text-prp-gold" />
                Vehicle Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Vehicle Name"
                  type="text"
                  placeholder="e.g., Toyota Camry"
                  {...register('vehicleName')}
                  error={errors.vehicleName?.message}
                  required
                />

                <Input
                  label="Vehicle ID"
                  type="text"
                  placeholder="e.g., VIN or License Plate"
                  {...register('vehicleId')}
                  error={errors.vehicleId?.message}
                  required
                />

                <Input
                  label="Vehicle Model"
                  type="text"
                  placeholder="e.g., Camry LE"
                  {...register('vehicleModel')}
                  error={errors.vehicleModel?.message}
                />

                <Input
                  label="Vehicle Year"
                  type="number"
                  placeholder="e.g., 2020"
                  {...register('vehicleYear', { valueAsNumber: true })}
                  error={errors.vehicleYear?.message}
                />

                <Input
                  label="Vehicle Color"
                  type="text"
                  placeholder="e.g., Silver"
                  {...register('vehicleColor')}
                  error={errors.vehicleColor?.message}
                />

                <Input
                  label="Mileage"
                  type="number"
                  placeholder="e.g., 50000"
                  {...register('mileage', { valueAsNumber: true })}
                  error={errors.mileage?.message}
                />

                <div>
                  <label className="block text-sm font-medium text-prp-light mb-2">
                    Vehicle Condition
                  </label>
                  <select
                    {...register('condition')}
                    className="w-full px-3 py-2 bg-prp-dark border border-prp-silver/30 rounded-lg text-prp-light focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-transparent"
                  >
                    <option value="excellent">Excellent</option>
                    <option value="good">Good</option>
                    <option value="fair">Fair</option>
                    <option value="poor">Poor</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div>
              <h2 className="text-xl font-bold text-prp-light mb-4">Customer Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Customer Name"
                  type="text"
                  placeholder="Customer's full name"
                  {...register('customerName')}
                  error={errors.customerName?.message}
                />

                <Input
                  label="Customer Phone"
                  type="tel"
                  placeholder="Customer's phone number"
                  {...register('customerPhone')}
                  error={errors.customerPhone?.message}
                />

                <Input
                  label="Customer Email"
                  type="email"
                  placeholder="Customer's email address"
                  {...register('customerEmail')}
                  error={errors.customerEmail?.message}
                />
              </div>
            </div>

            {/* Financial Information */}
            <div>
              <h2 className="text-xl font-bold text-prp-light mb-4 flex items-center">
                <DollarSign className="w-5 h-5 mr-2 text-prp-gold" />
                Financial Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Total Car Value"
                  type="number"
                  step="0.01"
                  placeholder="e.g., 25000"
                  {...register('totalCarValue', { valueAsNumber: true })}
                  error={errors.totalCarValue?.message}
                  required
                />

                <div>
                  <label className="block text-sm font-medium text-prp-light mb-2">
                    PRP Sales
                  </label>
                  <div className="w-full px-3 py-2 bg-prp-dark/50 border border-prp-silver/30 rounded-lg text-prp-gold font-bold text-lg">
                    {formatCurrency(prpSales)}
                  </div>
                </div>
              </div>
            </div>

            {/* Date and Pictures */}
            <div>
              <h2 className="text-xl font-bold text-prp-light mb-4 flex items-center">
                <Calendar className="w-5 h-5 mr-2 text-prp-gold" />
                Date & Documentation
              </h2>
              <div className="space-y-6">
                <Input
                  label="Date"
                  type="date"
                  {...register('date')}
                  error={errors.date?.message}
                  required
                />

                {/* Picture Upload */}
                <div>
                  <label className="block text-sm font-medium text-prp-light mb-2 flex items-center">
                    <Camera className="w-4 h-4 mr-2" />
                    Vehicle Pictures (Optional, Max 10)
                  </label>
                  <div className="border-2 border-dashed border-prp-silver/30 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="picture-upload"
                    />
                    <label
                      htmlFor="picture-upload"
                      className="cursor-pointer flex flex-col items-center"
                    >
                      <Upload className="w-8 h-8 text-prp-silver mb-2" />
                      <span className="text-prp-silver">Click to upload pictures</span>
                      <span className="text-prp-silver/70 text-sm">PNG, JPG up to 5MB each</span>
                    </label>
                  </div>

                  {/* Picture Preview */}
                  {pictures.length > 0 && (
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                      {pictures.map((file, index) => (
                        <div key={index} className="relative">
                          <img
                            src={URL.createObjectURL(file)}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() => removePicture(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            <X className="w-3 h-3" />
                          </button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Notes */}
            <div>
              <label className="block text-sm font-medium text-prp-light mb-2">
                Additional Notes
              </label>
              <textarea
                {...register('notes')}
                rows={4}
                placeholder="Any additional information about the vehicle or service..."
                className="w-full px-3 py-2 bg-prp-dark border border-prp-silver/30 rounded-lg text-prp-light placeholder-prp-silver/50 focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-transparent resize-none"
              />
              {errors.notes && (
                <p className="mt-1 text-sm text-red-500">{errors.notes.message}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-4">
              <Button
                type="button"
                variant="ghost"
                onClick={() => router.push('/dashboard')}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="primary"
                size="lg"
                loading={isLoading}
              >
                Submit Form
              </Button>
            </div>
          </form>
        </div>
      </main>
    </div>
  );
}
