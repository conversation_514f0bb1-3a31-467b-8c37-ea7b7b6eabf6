'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Logo from './Logo';
import Button from './ui/Button';
import { 
  Shield, 
  LogOut, 
  Menu, 
  X, 
  BarChart3, 
  Users, 
  FileText, 
  Settings,
  Bell
} from 'lucide-react';

interface AdminHeaderProps {
  onLogout?: () => void;
  currentPage?: string;
}

const AdminHeader: React.FC<AdminHeaderProps> = ({ onLogout, currentPage = 'dashboard' }) => {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/admin-logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      localStorage.removeItem('admin_token');
      localStorage.removeItem('admin_data');
      if (onLogout) onLogout();
      router.push('/admin/login');
    }
  };

  const navigationItems = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3, href: '/admin/dashboard' },
    { id: 'forms', label: 'Form Management', icon: FileText, href: '/admin/forms' },
    { id: 'users', label: 'Users', icon: Users, href: '/admin/users' },
    { id: 'analytics', label: 'Analytics', icon: BarChart3, href: '/admin/analytics' },
    { id: 'settings', label: 'Settings', icon: Settings, href: '/admin/settings' },
  ];

  return (
    <header className="bg-prp-dark/95 backdrop-blur-md border-b border-prp-gold/30 sticky top-0 z-50 shadow-lg shadow-prp-dark/50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo and Admin Badge */}
          <div className="flex items-center space-x-4">
            <Logo size="sm" showText={true} />
            <div className="hidden sm:flex items-center space-x-2 px-3 py-1 bg-prp-gold/20 border border-prp-gold/40 rounded-full">
              <Shield className="w-4 h-4 text-prp-gold" />
              <span className="text-prp-gold text-sm font-semibold">Admin Panel</span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-1">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              return (
                <button
                  key={item.id}
                  onClick={() => router.push(item.href)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-300 hover:scale-105 ${
                    isActive
                      ? 'bg-prp-gold/20 text-prp-gold border border-prp-gold/40 shadow-lg shadow-prp-gold/20'
                      : 'text-prp-silver hover:text-prp-gold hover:bg-prp-gold/10 hover:shadow-md hover:shadow-prp-gold/10'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span className="text-sm font-medium">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Logout Button */}
            <Button
              onClick={handleLogout}
              variant="outline"
              size="sm"
              className="hidden sm:flex items-center space-x-2"
            >
              <LogOut className="w-4 h-4" />
              <span>Logout</span>
            </Button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2 text-prp-silver hover:text-prp-gold transition-colors duration-200"
            >
              {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden py-4 border-t border-prp-silver/20">
            <div className="space-y-2">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;
                return (
                  <button
                    key={item.id}
                    onClick={() => {
                      router.push(item.href);
                      setIsMobileMenuOpen(false);
                    }}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-prp-gold/20 text-prp-gold border border-prp-gold/40'
                        : 'text-prp-silver hover:text-prp-gold hover:bg-prp-gold/10'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="font-medium">{item.label}</span>
                  </button>
                );
              })}
              
              {/* Mobile Logout */}
              <button
                onClick={handleLogout}
                className="w-full flex items-center space-x-3 px-4 py-3 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-all duration-200"
              >
                <LogOut className="w-5 h-5" />
                <span className="font-medium">Logout</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default AdminHeader;
