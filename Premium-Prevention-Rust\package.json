{"name": "premium-rust-prevention", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^6.4.17", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.539.0", "mongoose": "^8.17.1", "multer": "^2.0.2", "next": "15.4.6", "nodemailer": "^7.0.5", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "sweetalert2": "^11.22.3", "uuid": "^11.1.0", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.6", "tailwindcss": "^4", "typescript": "^5"}}