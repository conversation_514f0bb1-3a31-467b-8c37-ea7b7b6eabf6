/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminHeader from '@/components/AdminHeader';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import {
  FileText,
  Search,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Car,
  DollarSign,
  User,
  Building,
  Phone,
  Mail,
  Calendar,
  Image as ImageIcon
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

interface FormSubmission {
  id: string;
  vehicleName: string;
  vehicleId: string;
  vehicleModel?: string;
  vehicleYear?: number;
  vehicleColor?: string;
  totalCarValue: number;
  prpSales: number;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  mileage?: number;
  condition?: string;
  notes?: string;
  status: 'pending' | 'approved' | 'rejected';
  pictures: string[];
  date: string;
  createdAt: string;
  updatedAt: string;
  salesman: {
    id: string;
    name: string;
    email: string;
    dealership: string;
    phone?: string;
  };
}

export default function AdminFormsPage() {
  const router = useRouter();
  const [submissions, setSubmissions] = useState<FormSubmission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [selectedSubmission, setSelectedSubmission] = useState<FormSubmission | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check admin authentication
    const token = localStorage.getItem('admin_token');
    const adminInfo = localStorage.getItem('admin_data');

    if (!token || !adminInfo) {
      router.push('/admin/login');
      return;
    }

    try {
      const admin = JSON.parse(adminInfo);
      if (admin.role !== 'admin') {
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      router.push('/admin/login');
      return;
    }

    loadSubmissions();
  }, [router, currentPage, statusFilter, sortBy, sortOrder]);

  // Separate effect for search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setCurrentPage(1); // Reset to first page when searching
      loadSubmissions();
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Trigger animations after data loads
  useEffect(() => {
    if (submissions.length > 0 && !isLoading) {
      setIsVisible(true);
    }
  }, [submissions, isLoading]);

  const loadSubmissions = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('admin_token');
      
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '20',
        sortBy,
        sortOrder,
      });

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`/api/admin/forms?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setSubmissions(data.submissions);
        setTotalPages(data.pagination.pages);
      }
    } catch (error) {
      console.error('Failed to load submissions:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateSubmissionStatus = async (submissionId: string, newStatus: string, notes?: string) => {
    try {
      const token = localStorage.getItem('admin_token');
      const response = await fetch('/api/admin/forms', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          submissionId,
          status: newStatus,
          notes
        }),
      });

      if (response.ok) {
        // Reload submissions to reflect changes
        loadSubmissions();
        setShowModal(false);
        setSelectedSubmission(null);
      }
    } catch (error) {
      console.error('Failed to update submission status:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-400" />;
      case 'approved':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'rejected':
        return <XCircle className="w-4 h-4 text-red-400" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-500/10 border-yellow-500/30 text-yellow-400';
      case 'approved':
        return 'bg-green-500/10 border-green-500/30 text-green-400';
      case 'rejected':
        return 'bg-red-500/10 border-red-500/30 text-red-400';
      default:
        return 'bg-gray-500/10 border-gray-500/30 text-gray-400';
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_data');
    router.push('/admin/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
        <AdminHeader onLogout={handleLogout} currentPage="forms" />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-prp-gold mx-auto mb-4"></div>
            <p className="text-prp-silver">Loading form submissions...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-prp-gold/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <AdminHeader onLogout={handleLogout} currentPage="forms" />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        {/* Header */}
        <div className={`mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center group">
            <FileText className="w-10 h-10 mr-3 text-prp-gold transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
            <span className="bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
              Form Submissions Management
            </span>
          </h1>
          <p className="text-prp-silver text-lg">
            Review and manage all salesman form submissions with status updates.
          </p>
        </div>

        {/* Filters and Search */}
        <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 mb-8 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
             style={{ transitionDelay: '200ms' }}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative group">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver group-focus-within:text-prp-gold transition-colors duration-300" />
              <input
                type="text"
                placeholder="Search by vehicle name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full bg-prp-dark border border-prp-silver/30 text-prp-light rounded-lg pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-prp-gold/50 transition-all duration-300 hover:border-prp-gold/30"
              />
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="bg-prp-dark border border-prp-silver/30 text-prp-light rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-prp-gold/50 transition-all duration-300 hover:border-prp-gold/30 hover:scale-105"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
            </select>

            {/* Date Sorting */}
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [newSortBy, newSortOrder] = e.target.value.split('-');
                setSortBy(newSortBy);
                setSortOrder(newSortOrder);
              }}
              className="bg-prp-dark border border-prp-silver/30 text-prp-light rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-prp-gold/50 transition-all duration-300 hover:border-prp-gold/30 hover:scale-105"
            >
              <option value="createdAt-desc">Newest First</option>
              <option value="createdAt-asc">Oldest First</option>
            </select>
          </div>
        </div>

        {/* Submissions Table */}
        <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl overflow-hidden shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
             style={{ transitionDelay: '400ms' }}>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-prp-dark/80 border-b border-prp-silver/20">
                <tr>
                  <th className="px-6 py-4 text-left text-sm font-medium text-prp-light">Vehicle</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-prp-light">Salesman</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-prp-light">Values</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-prp-light">Status</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-prp-light">Date</th>
                  <th className="px-6 py-4 text-left text-sm font-medium text-prp-light">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-prp-silver/10">
                {submissions.map((submission, index) => (
                  <tr
                    key={submission.id}
                    className={`hover:bg-prp-dark/40 transition-all duration-300 hover:scale-[1.01] hover:shadow-lg hover:shadow-prp-gold/10 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'}`}
                    style={{ transitionDelay: `${600 + index * 100}ms` }}
                  >
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <Car className="w-5 h-5 text-prp-gold" />
                        <div>
                          <p className="text-prp-light font-medium">{submission.vehicleName}</p>
                          <p className="text-prp-silver text-sm">ID: {submission.vehicleId}</p>
                          {submission.vehicleModel && (
                            <p className="text-prp-silver text-xs">
                              {submission.vehicleYear} {submission.vehicleModel}
                            </p>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3">
                        <User className="w-5 h-5 text-prp-silver" />
                        <div>
                          <p className="text-prp-light font-medium">{submission.salesman.name}</p>
                          <p className="text-prp-silver text-sm">{submission.salesman.dealership}</p>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <DollarSign className="w-4 h-4 text-prp-gold" />
                          <span className="text-prp-light font-medium">
                            {formatCurrency(submission.totalCarValue)}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-prp-silver text-sm">PRP:</span>
                          <span className="text-prp-gold text-sm font-medium">
                            {formatCurrency(submission.prpSales)}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full border transition-all duration-300 hover:scale-110 hover:shadow-lg ${getStatusColor(submission.status)}`}>
                        <span className="transition-transform duration-300 hover:scale-125">
                          {getStatusIcon(submission.status)}
                        </span>
                        <span className="text-sm font-medium capitalize">{submission.status}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4 text-prp-silver" />
                        <span className="text-prp-silver text-sm">
                          {formatDateTime(submission.createdAt)}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2">
                        {submission.status === 'pending' && (
                          <>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => updateSubmissionStatus(submission.id, 'approved')}
                              className="text-green-400 hover:text-green-300 transition-all duration-300 hover:scale-110"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => updateSubmissionStatus(submission.id, 'rejected')}
                              className="text-red-400 hover:text-red-300 transition-all duration-300 hover:scale-110"
                            >
                              <XCircle className="w-4 h-4" />
                            </Button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="px-6 py-4 border-t border-prp-silver/20 flex items-center justify-between">
              <div className="text-prp-silver text-sm">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
