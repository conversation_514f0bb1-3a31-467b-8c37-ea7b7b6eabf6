/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-unused-vars */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminHeader from '@/components/AdminHeader';
import {
  BarChart3,
  TrendingUp,
  DollarSign,
  Users,
  Building,
  Calendar,
  PieChart,
  Activity,
  Target,
  Award
} from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// Animated Counter Component
const AnimatedCounter: React.FC<{ end: number; duration?: number; delay?: number; prefix?: string }> = ({
  end,
  duration = 2000,
  delay = 0,
  prefix = ''
}) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasStarted(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (!hasStarted) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, hasStarted]);

  return <span>{prefix}{count.toLocaleString()}</span>;
};

interface AnalyticsData {
  totalRevenue: number;
  totalSubmissions: number;
  totalUsers: number;
  averageCarValue: number;
  averagePrpSales: number;
  monthlyData: Array<{
    month: string;
    submissions: number;
    revenue: number;
  }>;
  dealershipBreakdown: Array<{
    dealership: string;
    submissions: number;
    revenue: number;
  }>;
  salesmanPerformance: Array<{
    name: string;
    dealership: string;
    submissions: number;
    revenue: number;
  }>;
  statusBreakdown: {
    pending: number;
    approved: number;
    rejected: number;
  };
}

export default function AdminAnalyticsPage() {
  const router = useRouter();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('6months');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check admin authentication
    const token = localStorage.getItem('admin_token');
    const adminInfo = localStorage.getItem('admin_data');
    
    if (!token || !adminInfo) {
      router.push('/admin/login');
      return;
    }

    try {
      const admin = JSON.parse(adminInfo);
      if (admin.role !== 'admin') {
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      router.push('/admin/login');
      return;
    }

    loadAnalytics();
  }, [router, timeRange]);

  // Trigger animations after data loads
  useEffect(() => {
    if (analytics && !isLoading) {
      setIsVisible(true);
    }
  }, [analytics, isLoading]);

  const loadAnalytics = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch(`/api/admin/analytics?timeRange=${timeRange}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_data');
    router.push('/admin/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
        <AdminHeader onLogout={handleLogout} currentPage="analytics" />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-prp-gold mx-auto mb-4"></div>
            <p className="text-prp-silver animate-pulse">Loading analytics...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-prp-gold/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <AdminHeader onLogout={handleLogout} currentPage="analytics" />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        {/* Header */}
        <div className={`mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center group">
                <BarChart3 className="w-10 h-10 mr-3 text-prp-gold transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
                <span className="bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
                  Analytics Dashboard
                </span>
              </h1>
              <p className="text-prp-silver text-lg">
                Comprehensive analytics and performance insights for Premium Rust Prevention.
              </p>
            </div>
            <div>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="bg-prp-dark border border-prp-silver/30 text-prp-light rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-prp-gold/50 transition-all duration-300 hover:border-prp-gold/30 hover:scale-105"
              >
                <option value="1month">Last Month</option>
                <option value="3months">Last 3 Months</option>
                <option value="6months">Last 6 Months</option>
                <option value="1year">Last Year</option>
                <option value="all">All Time</option>
              </select>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Revenue */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-prp-gold/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '100ms' }}>
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-prp-gold/20 rounded-lg group-hover:bg-prp-gold/30 transition-all duration-300 group-hover:scale-110">
                <DollarSign className="w-6 h-6 text-prp-gold group-hover:text-yellow-300 transition-colors duration-300" />
              </div>
              <span className="text-2xl font-bold text-prp-light group-hover:text-prp-gold transition-colors duration-300">
                $<AnimatedCounter end={analytics?.totalRevenue || 0} delay={200} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Total Revenue</h3>
            <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">PRP commission earnings</p>
          </div>

          {/* Total Submissions */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <Activity className="w-6 h-6 text-blue-400" />
              </div>
              <span className="text-2xl font-bold text-prp-light">{analytics?.totalSubmissions || 0}</span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Total Submissions</h3>
            <p className="text-xs text-prp-silver/70 mt-1">Form submissions processed</p>
          </div>

          {/* Active Salesmen */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <Users className="w-6 h-6 text-green-400" />
              </div>
              <span className="text-2xl font-bold text-prp-light">{analytics?.totalUsers || 0}</span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Active Salesmen</h3>
            <p className="text-xs text-prp-silver/70 mt-1">Registered users</p>
          </div>

          {/* Average Car Value */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500/20 rounded-lg">
                <Target className="w-6 h-6 text-purple-400" />
              </div>
              <span className="text-2xl font-bold text-prp-light">
                {formatCurrency(analytics?.averageCarValue || 0)}
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Avg Car Value</h3>
            <p className="text-xs text-prp-silver/70 mt-1">Average vehicle value</p>
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Monthly Revenue Chart */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <TrendingUp className="w-6 h-6 mr-2 text-prp-gold" />
              Monthly Revenue Trend
            </h3>
            <div className="space-y-4">
              {analytics?.monthlyData.map((month, index) => (
                <div key={month.month} className="flex items-center justify-between">
                  <span className="text-prp-silver text-sm font-medium">{month.month}</span>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div
                        className="h-3 bg-prp-gold rounded-full mr-2"
                        style={{
                          width: `${Math.max(20, (month.revenue / Math.max(...(analytics?.monthlyData.map(m => m.revenue) || [1]))) * 120)}px`
                        }}
                      ></div>
                      <span className="text-prp-light text-sm font-medium">
                        {formatCurrency(month.revenue)}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Status Breakdown */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <PieChart className="w-6 h-6 mr-2 text-prp-gold" />
              Submission Status
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <span className="text-yellow-400 font-medium">Pending</span>
                <span className="text-2xl font-bold text-yellow-400">
                  {analytics?.statusBreakdown.pending || 0}
                </span>
              </div>
              <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                <span className="text-green-400 font-medium">Approved</span>
                <span className="text-2xl font-bold text-green-400">
                  {analytics?.statusBreakdown.approved || 0}
                </span>
              </div>
              <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                <span className="text-red-400 font-medium">Rejected</span>
                <span className="text-2xl font-bold text-red-400">
                  {analytics?.statusBreakdown.rejected || 0}
                </span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
