'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Logo from '@/components/Logo';
import Button from '@/components/ui/Button';

export default function Home() {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(false);

  // Removed automatic login check - users should manually log in
  // This prevents auto-login after logout

  // Trigger animations after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark flex items-center justify-center px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-prp-gold/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-prp-gold/3 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="max-w-4xl mx-auto text-center relative z-10">
        {/* Logo and Header */}
        <div className={`mb-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="flex justify-center mb-8">
            <div className={`transition-all duration-1000 hover:scale-110 ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
                 style={{ transitionDelay: '200ms' }}>
              <Logo size="lg" showText={true} />
            </div>
          </div>
          <h1 className={`text-4xl md:text-6xl font-bold text-prp-light mb-6 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
              style={{ transitionDelay: '400ms' }}>
            <span className="bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
              Professional Vehicle
            </span>
            <span className="block text-prp-gold bg-gradient-to-r from-prp-gold to-yellow-300 bg-clip-text text-transparent">
              Rust Prevention
            </span>
          </h1>
          <p className={`text-xl text-prp-silver max-w-2xl mx-auto leading-relaxed transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
             style={{ transitionDelay: '600ms' }}>
            Advanced coating technology for automotive dealerships.
            Track your sales, manage submissions, and protect vehicles with our comprehensive rust prevention solutions.
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:border-prp-gold/50 hover:scale-105 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '800ms' }}>
            <div className="w-12 h-12 bg-prp-gold/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-prp-gold/30 transition-colors duration-300 group-hover:scale-110">
              <svg className="w-6 h-6 text-prp-gold group-hover:text-yellow-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-prp-light mb-2 group-hover:text-prp-gold transition-colors duration-300">Easy Tracking</h3>
            <p className="text-prp-silver text-sm group-hover:text-prp-light transition-colors duration-300">
              Submit and track your vehicle forms with real-time status updates and comprehensive statistics.
            </p>
          </div>

          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:border-prp-gold/50 hover:scale-105 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '900ms' }}>
            <div className="w-12 h-12 bg-prp-gold/20 rounded-lg flex items-center justify-center mx-auto mb-4 group-hover:bg-prp-gold/30 transition-colors duration-300 group-hover:scale-110">
              <svg className="w-6 h-6 text-prp-gold group-hover:text-yellow-300 transition-colors duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-prp-light mb-2 group-hover:text-prp-gold transition-colors duration-300">Sales Tracking</h3>
            <p className="text-prp-silver text-sm group-hover:text-prp-light transition-colors duration-300">
              Automatically calculate your Sales on total vehicle value with detailed earnings reports.
            </p>
          </div>

          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 hover:border-prp-gold/50 hover:scale-105 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '1000ms' }}>
            <div className="w-12 h-12 bg-prp-gold/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-prp-gold" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-prp-light mb-2">Premium Protection</h3>
            <p className="text-prp-silver text-sm">
              Industry-leading rust prevention technology that extends vehicle life and maintains value.
            </p>
          </div>
        </div>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button
            onClick={() => router.push('/signup')}
            variant="primary"
            size="lg"
            className="px-8 py-4 text-lg"
          >
            Get Started Today
          </Button>
          <Button
            onClick={() => router.push('/login')}
            variant="outline"
            size="lg"
            className="px-8 py-4 text-lg"
          >
            Sign In
          </Button>
        </div>

        {/* Footer */}
        <div className="mt-16 pt-8 border-t border-prp-silver/20">
          <p className="text-prp-silver/70 text-sm">
            © 2024 Premium Rust Prevention. Professional vehicle protection solutions.
          </p>
        </div>
      </div>
    </div>
  );
}
