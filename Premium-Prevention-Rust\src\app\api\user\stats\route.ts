import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import FormSubmission from '@/models/FormSubmission';
import { verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    // Verify token and extract user ID
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token' },
        { status: 401 }
      );
    }

    const userId = payload.userId;

    // Get all form submissions for the user
    const submissions = await FormSubmission.find({ userId })
      .sort({ createdAt: -1 })
      .lean();

    // Calculate statistics
    const totalSubmissions = submissions.length;
    const totalRevenue = submissions.reduce((sum, sub) => sum + sub.prpSales, 0);
    const totalCarValue = submissions.reduce((sum, sub) => sum + sub.totalCarValue, 0);
    const highestPrpSale = submissions.length > 0 ? Math.max(...submissions.map(sub => sub.prpSales)) : 0;
    const averageCarValue = totalSubmissions > 0 ? totalCarValue / totalSubmissions : 0;

    // Status breakdown
    const pendingSubmissions = submissions.filter(sub => sub.status === 'pending').length;
    const approvedSubmissions = submissions.filter(sub => sub.status === 'approved').length;
    const rejectedSubmissions = submissions.filter(sub => sub.status === 'rejected').length;

    const stats = {
      totalSubmissions,
      totalRevenue,
      highestPrpSale,
      averageCarValue,
      pendingSubmissions,
      approvedSubmissions,
      rejectedSubmissions
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('User stats API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
