/* eslint-disable @typescript-eslint/no-explicit-any */
'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminHeader from '@/components/AdminHeader';
import Button from '@/components/ui/Button';
import {
  Settings,
  Shield,
  Mail,
  Database,
  Bell,
  Key,
  Info
} from 'lucide-react';

export default function AdminSettingsPage() {
  const router = useRouter();
  const [adminData, setAdminData] = useState<any>(null);

  useEffect(() => {
    // Check admin authentication
    const token = localStorage.getItem('admin_token');
    const adminInfo = localStorage.getItem('admin_data');
    
    if (!token || !adminInfo) {
      router.push('/admin/login');
      return;
    }

    try {
      const admin = JSON.parse(adminInfo);
      if (admin.role !== 'admin') {
        router.push('/admin/login');
        return;
      }
      setAdminData(admin);
    } catch (error) {
      router.push('/admin/login');
      return;
    }
  }, [router]);

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_data');
    router.push('/admin/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
      <AdminHeader onLogout={handleLogout} currentPage="settings" />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center">
            <Settings className="w-10 h-10 mr-3 text-prp-gold" />
            Admin Settings
          </h1>
          <p className="text-prp-silver">
            Configure and manage your admin panel settings.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Admin Profile */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <Shield className="w-6 h-6 mr-2 text-prp-gold" />
              Admin Profile
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-prp-silver mb-2">Name</label>
                <div className="bg-prp-dark border border-prp-silver/30 rounded-lg px-3 py-2">
                  <span className="text-prp-light">{adminData?.name || 'Admin'}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-prp-silver mb-2">Email</label>
                <div className="bg-prp-dark border border-prp-silver/30 rounded-lg px-3 py-2">
                  <span className="text-prp-light">{adminData?.email}</span>
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-prp-silver mb-2">Role</label>
                <div className="bg-prp-dark border border-prp-silver/30 rounded-lg px-3 py-2">
                  <span className="text-prp-gold font-medium">Administrator</span>
                </div>
              </div>
            </div>
          </div>

          {/* System Information */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <Info className="w-6 h-6 mr-2 text-prp-gold" />
              System Information
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-prp-silver">System Version</span>
                <span className="text-prp-light">v1.0.0</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-prp-silver">Last Updated</span>
                <span className="text-prp-light">{new Date().toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-prp-silver">Database Status</span>
                <span className="text-green-400">Connected</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-prp-silver">Email Service</span>
                <span className="text-green-400">Active</span>
              </div>
            </div>
          </div>


        </div>



        {/* Warning Notice */}
        <div className="mt-8 bg-yellow-500/10 border border-yellow-500/30 rounded-xl p-6">
          <div className="flex items-start space-x-3">
            <Shield className="w-6 h-6 text-yellow-400 mt-1" />
            <div>
              <h4 className="text-yellow-400 font-bold mb-2">Security Notice</h4>
              <p className="text-prp-silver text-sm">
                This admin panel has full access to all system data and user information. 
                Please ensure your credentials are kept secure and never shared with unauthorized personnel.
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
