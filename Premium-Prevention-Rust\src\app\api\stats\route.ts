import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import FormSubmission from '@/models/FormSubmission';
import { verifyToken } from '@/lib/auth';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.replace('Bearer ', '');

    // If no token in header, try cookies
    if (!token) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);
        token = cookies['auth_token'];
      }
    }

    // If still no token, try .env file as fallback
    if (!token) {
      try {
        const envPath = path.join(process.cwd(), '.env.local');
        const envContent = fs.readFileSync(envPath, 'utf8');
        const tokenLine = envContent.split('\n').find(line => line.startsWith('USER_TOKEN='));
        if (tokenLine) {
          token = tokenLine.split('=')[1];
        }
      } catch (error) {
        // No token found
      }
    }

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided. Please log in again.' },
        { status: 401 }
      );
    }

    // Verify token and extract user ID
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token. Please log in again.' },
        { status: 401 }
      );
    }

    const userId = payload.userId;

    // Get all form submissions for the user
    const submissions = await FormSubmission.find({ userId })
      .sort({ createdAt: -1 })
      .lean();

    // Calculate statistics
    const totalSubmissions = submissions.length;
    const totalCarValue = submissions.reduce((sum, sub) => sum + sub.totalCarValue, 0);
    const totalPrpSales = submissions.reduce((sum, sub) => sum + sub.prpSales, 0);

    // Status breakdown
    const statusBreakdown = {
      pending: submissions.filter(sub => sub.status === 'pending').length,
      approved: submissions.filter(sub => sub.status === 'approved').length,
      rejected: submissions.filter(sub => sub.status === 'rejected').length,
    };

    // Monthly breakdown (last 12 months)
    const monthlyData = [];
    const now = new Date();
    for (let i = 11; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const nextMonth = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
      
      const monthSubmissions = submissions.filter(sub => {
        const subDate = new Date(sub.createdAt);
        return subDate >= date && subDate < nextMonth;
      });

      monthlyData.push({
        month: date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        submissions: monthSubmissions.length,
        totalValue: monthSubmissions.reduce((sum, sub) => sum + sub.totalCarValue, 0),
        prpSales: monthSubmissions.reduce((sum, sub) => sum + sub.prpSales, 0),
      });
    }

    // Recent submissions (last 10)
    const recentSubmissions = submissions.slice(0, 10).map(sub => ({
      id: sub._id,
      vehicleName: sub.vehicleName,
      vehicleId: sub.vehicleId,
      totalCarValue: sub.totalCarValue,
      prpSales: sub.prpSales,
      status: sub.status,
      createdAt: sub.createdAt,
      customerName: sub.customerName,
      vehicleModel: sub.vehicleModel,
      vehicleYear: sub.vehicleYear,
    }));

    // Vehicle condition breakdown
    const conditionBreakdown = {
      excellent: submissions.filter(sub => sub.condition === 'excellent').length,
      good: submissions.filter(sub => sub.condition === 'good').length,
      fair: submissions.filter(sub => sub.condition === 'fair').length,
      poor: submissions.filter(sub => sub.condition === 'poor').length,
    };

    // Average values
    const averageCarValue = totalSubmissions > 0 ? totalCarValue / totalSubmissions : 0;
    const averagePrpSales = totalSubmissions > 0 ? totalPrpSales / totalSubmissions : 0;

    const stats = {
      totalSubmissions,
      totalCarValue,
      totalPrpSales,
      averageCarValue,
      averagePrpSales,
      statusBreakdown,
      conditionBreakdown,
      monthlyData,
      recentSubmissions,
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Stats API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
