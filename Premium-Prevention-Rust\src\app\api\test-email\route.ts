import { NextRequest, NextResponse } from 'next/server';
import { testEmailConnection, sendPasswordResetEmail } from '@/lib/email';

export async function GET(request: NextRequest) {
  try {
    // Test email connection
    const connectionTest = await testEmailConnection();
    
    if (!connectionTest.success) {
      return NextResponse.json(
        { 
          error: 'Email connection failed', 
          details: connectionTest.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { 
        message: 'Email configuration is working correctly!',
        connection: 'successful'
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Email test error:', error);
    return NextResponse.json(
      { error: 'Email test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Send test email
    const result = await sendPasswordResetEmail(email, '1234');
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Failed to send test email', 
          details: result.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { 
        message: 'Test email sent successfully!',
        messageId: result.messageId
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Email send test error:', error);
    return NextResponse.json(
      { error: 'Email send test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
