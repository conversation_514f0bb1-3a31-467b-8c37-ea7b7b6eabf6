'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Button from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import {
  Car,
  Info,
  HelpCircle,
  User,
  FileText,
  DollarSign,
  TrendingUp,
  CheckCircle,
  Clock,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

// Animated Counter Component
const AnimatedCounter: React.FC<{ end: number; duration?: number; delay?: number; prefix?: string; suffix?: string }> = ({
  end,
  duration = 2000,
  delay = 0,
  prefix = '',
  suffix = ''
}) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasStarted(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (!hasStarted) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, hasStarted]);

  return <span>{prefix}{count.toLocaleString()}{suffix}</span>;
};

interface UserStats {
  totalSubmissions: number;
  totalRevenue: number;
  highestPrpSale: number;
  averageCarValue: number;
  pendingSubmissions: number;
  approvedSubmissions: number;
  rejectedSubmissions: number;
}

export default function DashboardPage() {
  const router = useRouter();
  const { user } = useAuth();
  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const faqData = [
    {
      question: "How is PRP sales calculated?",
      answer: "PRP sales is calculated based on the total car value. for Sales of $50,000 or less, the PRP sales is $2,500. otherwise it is 5% of the total car value."
    },
    {
      question: "How long does approval take?",
      answer: "Form submissions are typically reviewed and approved within 24-48 hours. You'll receive email notifications about status changes. Urgent submissions may be processed faster during business hours."
    },
    {
      question: "Can I edit submitted forms?",
      answer: "Once submitted, forms cannot be edited directly. Please ensure all information is accurate before submission. If corrections are needed, contact our support team who can help you with the necessary changes."
    },
    {
      question: "What documents do I need for submission?",
      answer: "You'll need vehicle photos, customer information, vehicle details (VIN, model, year), and the total car value. Make sure all photos are clear and show the vehicle's condition accurately."
    },
    {
      question: "How do I track my sales?",
      answer: "You can track your sales in the Performance Stats section above, or visit the dedicated Stats page. All approved submissions contribute to your total revenue, which is updated in real-time."
    },
    {
      question: "Need Support?",
      answer: "Contact our support <NAME_EMAIL> for any questions or assistance with your rust prevention services and form submissions. We're here to help you succeed!"
    }
  ];

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  useEffect(() => {
    if (user) {
      loadProfileImage();
      loadUserStats();
    }
  }, [user]);

  // Trigger animations after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const loadUserStats = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to load user stats:', error);
    }
  };

  const loadProfileImage = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/profile-image', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setProfileImage(data.profileImage);
      }
    } catch (error) {
      console.error('Failed to load profile image:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      router.push('/login');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-prp-gold/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <Header onLogout={handleLogout} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        {/* Welcome Section */}
        <div className={`mb-12 text-center transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-prp-light mb-4 bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
              Premium Rust Prevention
            </h1>
            <p className="text-2xl text-prp-silver">
              Professional Vehicle Protection Dashboard
            </p>
          </div>
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-gold/30 rounded-2xl p-8 max-w-4xl mx-auto shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '200ms' }}>
            <div className="flex items-center justify-center mb-6">
              <Car className="w-16 h-16 text-prp-gold group-hover:scale-110 group-hover:rotate-12 transition-transform duration-300" />
            </div>
            <h2 className="text-3xl font-bold text-prp-light mb-4 group-hover:text-prp-gold transition-colors duration-300">Dashboard</h2>
            <p className="text-prp-silver text-lg leading-relaxed group-hover:text-prp-light transition-colors duration-300">
              Your comprehensive platform for managing automotive protection services and rust prevention solutions.
              Access all your tools and track your performance from this central hub.
            </p>
          </div>
        </div>

        {/* Performance Stats Section */}
        {stats && (
          <div className={`mb-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '300ms' }}>
            <h2 className="text-3xl font-bold text-prp-light mb-8 text-center bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
              Your Performance Stats
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Total Submissions */}
              <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-blue-400/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                   style={{ transitionDelay: '400ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-blue-500/20 rounded-lg group-hover:bg-blue-500/30 transition-all duration-300 group-hover:scale-110">
                    <FileText className="w-6 h-6 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
                  </div>
                  <span className="text-2xl font-bold text-prp-light group-hover:text-blue-300 transition-colors duration-300">
                    <AnimatedCounter end={stats.totalSubmissions} delay={600} />
                  </span>
                </div>
                <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Total Forms</h3>
                <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">All time submissions</p>
              </div>

              {/* Total Revenue */}
              <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-prp-gold/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                   style={{ transitionDelay: '500ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-prp-gold/20 rounded-lg group-hover:bg-prp-gold/30 transition-all duration-300 group-hover:scale-110">
                    <DollarSign className="w-6 h-6 text-prp-gold group-hover:text-yellow-300 transition-colors duration-300" />
                  </div>
                  <span className="text-2xl font-bold text-prp-light group-hover:text-prp-gold transition-colors duration-300">
                    $<AnimatedCounter end={stats.totalRevenue} delay={700} />
                  </span>
                </div>
                <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Total Revenue</h3>
                <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">PRP sales</p>
              </div>

              {/* Approved Forms */}
              <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-green-400/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                   style={{ transitionDelay: '600ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-green-500/20 rounded-lg group-hover:bg-green-500/30 transition-all duration-300 group-hover:scale-110">
                    <CheckCircle className="w-6 h-6 text-green-400 group-hover:text-green-300 transition-colors duration-300" />
                  </div>
                  <span className="text-2xl font-bold text-prp-light group-hover:text-green-300 transition-colors duration-300">
                    <AnimatedCounter end={stats.approvedSubmissions} delay={800} />
                  </span>
                </div>
                <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Approved</h3>
                <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">Successful submissions</p>
              </div>

              {/* Pending Forms */}
              <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-yellow-400/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                   style={{ transitionDelay: '700ms' }}>
                <div className="flex items-center justify-between mb-4">
                  <div className="p-3 bg-yellow-500/20 rounded-lg group-hover:bg-yellow-500/30 transition-all duration-300 group-hover:scale-110">
                    <Clock className="w-6 h-6 text-yellow-400 group-hover:text-yellow-300 transition-colors duration-300" />
                  </div>
                  <span className="text-2xl font-bold text-prp-light group-hover:text-yellow-300 transition-colors duration-300">
                    <AnimatedCounter end={stats.pendingSubmissions} delay={900} />
                  </span>
                </div>
                <h3 className="text-prp-silver text-sm font-medium group-hover:text-prp-light transition-colors duration-300">Pending</h3>
                <p className="text-xs text-prp-silver/70 mt-1 group-hover:text-prp-silver transition-colors duration-300">Awaiting review</p>
              </div>
            </div>
          </div>
        )}

        {/* Professional Info Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Service Overview */}
          <div className={`bg-prp-dark/50 backdrop-blur-sm border border-prp-silver/20 rounded-xl p-8 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '400ms' }}>
            <h2 className="text-2xl font-bold text-prp-light mb-6 flex items-center group-hover:text-prp-gold transition-colors duration-300">
              <Car className="w-6 h-6 mr-3 text-prp-gold group-hover:scale-110 transition-transform duration-300" />
              Our Services
            </h2>
            <div className="space-y-4">
              <div className="p-4 bg-prp-dark/40 rounded-lg hover:bg-prp-dark/60 transition-colors duration-300">
                <h3 className="text-prp-light font-semibold mb-2 group-hover:text-prp-gold transition-colors duration-300">Vehicle Rust Prevention</h3>
                <p className="text-prp-silver text-sm group-hover:text-prp-light transition-colors duration-300">
                  Advanced coating technology to protect vehicles from rust and corrosion.
                </p>
              </div>
              <div className="p-4 bg-prp-dark/40 rounded-lg hover:bg-prp-dark/60 transition-colors duration-300">
                <h3 className="text-prp-light font-semibold mb-2 group-hover:text-prp-gold transition-colors duration-300">Professional Application</h3>
                <p className="text-prp-silver text-sm group-hover:text-prp-light transition-colors duration-300">
                  Expert application ensuring maximum protection and longevity.
                </p>
              </div>
            </div>
          </div>

          {/* Performance Metrics */}
          <div className={`bg-prp-dark/50 backdrop-blur-sm border border-prp-silver/20 rounded-xl p-8 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '600ms' }}>
            <h2 className="text-2xl font-bold text-prp-light mb-6 flex items-center group-hover:text-prp-gold transition-colors duration-300">
              <Info className="w-6 h-6 mr-3 text-prp-gold group-hover:scale-110 transition-transform duration-300" />
              System Information
            </h2>
            <div className="space-y-4">
              <div className="p-4 bg-prp-dark/40 rounded-lg hover:bg-prp-dark/60 transition-colors duration-300">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-prp-light font-semibold group-hover:text-prp-gold transition-colors duration-300">
                    {stats ? 'Average Car Value' : 'Submission Tracking'}
                  </h3>
                  {stats && (
                    <span className="text-2xl font-bold text-prp-light">
                      $<AnimatedCounter end={Math.round(stats.averageCarValue)} delay={1200} />
                    </span>
                  )}
                </div>
                <p className="text-prp-silver text-sm group-hover:text-prp-light transition-colors duration-300">
                  {stats ? 'Average value of vehicles processed' : 'Real-time tracking of all form submissions and approval status.'}
                </p>
              </div>
            </div>
          </div>
        </div>

        
        {/* Modern FAQ Section */}
        <div className={`mt-12 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
             id="faq-section" style={{ transitionDelay: '800ms' }}>
          <div className="bg-prp-dark/50 backdrop-blur-sm border border-prp-silver/20 rounded-xl p-8 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500">
            <h2 className="text-3xl font-bold text-prp-light mb-8 text-center flex items-center justify-center">
              <HelpCircle className="w-8 h-8 mr-3 text-prp-gold" />
              <span className="bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
                Frequently Asked Questions
              </span>
            </h2>

            <div className="max-w-4xl mx-auto space-y-4">
              {faqData.map((faq, index) => (
                <div
                  key={index}
                  className={`bg-prp-dark/40 backdrop-blur-sm border border-prp-silver/30 rounded-xl overflow-hidden transition-all duration-500 hover:border-prp-gold/50 hover:shadow-lg hover:shadow-prp-gold/10 ${isVisible ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-8'}`}
                  style={{ transitionDelay: `${900 + index * 100}ms` }}
                >
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="w-full p-6 text-left flex items-center justify-between hover:bg-prp-dark/60 transition-all duration-300 group"
                  >
                    <h3 className="text-lg font-semibold text-prp-light group-hover:text-prp-gold transition-colors duration-300">
                      {faq.question}
                    </h3>
                    <div className="flex-shrink-0 ml-4">
                      {openFAQ === index ? (
                        <ChevronUp className="w-5 h-5 text-prp-gold transition-transform duration-300 group-hover:scale-110" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-prp-silver group-hover:text-prp-gold transition-all duration-300 group-hover:scale-110" />
                      )}
                    </div>
                  </button>

                  <div className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    openFAQ === index ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                  }`}>
                    <div className="px-6 pb-6">
                      <div className="border-t border-prp-silver/20 pt-4">
                        <p className="text-prp-silver leading-relaxed animate-fade-in">
                          {faq.answer}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Contact Support CTA */}
            <div className="mt-8 text-center">
              <div className="bg-prp-gold/10 border border-prp-gold/30 rounded-xl p-6 max-w-2xl mx-auto">
                <h3 className="text-xl font-bold text-prp-gold mb-2">Still have questions?</h3>
                <p className="text-prp-silver mb-4">
                  Our support team is here to help you with any questions about Premium Rust Prevention services.
                </p>
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center px-6 py-3 bg-prp-gold text-prp-dark font-semibold rounded-lg hover:bg-yellow-400 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  Contact Support
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
