# Email Configuration Troubleshooting Guide

## Current Status
✅ **Password Updated**: Changed from `Honda008!` to `Chasemia2015!`
✅ **Configuration Updated**: Using SSL (port 465) with proper settings
❌ **Authentication Issue**: Still getting "535 5.7.8 Error: authentication failed"

## Current Configuration
```javascript
{
  host: "mail.privateemail.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "Chasemia2015!"
  },
  tls: {
    rejectUnauthorized: false
  }
}
```

## Troubleshooting Steps

### 1. Verify Email Account Setup
- [ ] Log into Namecheap cPanel/Webmail with `<EMAIL>`
- [ ] Confirm the password `Chasemia2015!` works for webmail login
- [ ] Check if the email account is active and not suspended

### 2. Check SMTP Settings in Namecheap
- [ ] Go to Namecheap cPanel → Email Accounts
- [ ] Verify SMTP is enabled for the account
- [ ] Check if there are any IP restrictions
- [ ] Ensure "Allow less secure apps" is enabled (if available)

### 3. Alternative SMTP Settings to Try

#### Option A: TLS Configuration
```javascript
{
  host: "mail.privateemail.com",
  port: 587,
  secure: false,
  auth: {
    user: "<EMAIL>",
    pass: "Chasemia2015!"
  }
}
```

#### Option B: Alternative Host
```javascript
{
  host: "premiumrustprevention.com",
  port: 465,
  secure: true,
  auth: {
    user: "<EMAIL>",
    pass: "Chasemia2015!"
  }
}
```

### 4. Check for 2FA/App Passwords
- [ ] Check if 2-Factor Authentication is enabled
- [ ] If yes, generate an app-specific password
- [ ] Use the app password instead of the regular password

### 5. Contact Namecheap Support
If the above steps don't work, contact Namecheap support with:
- Domain: premiumrustprevention.com
- Email: <EMAIL>
- Issue: Cannot authenticate SMTP for sending emails
- Error: "535 5.7.8 Error: authentication failed"

### 6. Temporary Workaround
If the issue persists, we can temporarily use a working email service:

#### Gmail Configuration (Backup)
```javascript
{
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'your-app-password'
  }
}
```

## Testing the Configuration
Run this command to test email after making changes:
```bash
npm run dev
```
Then visit: `http://localhost:3000/api/test-email`

## Next Steps
1. Try the troubleshooting steps above
2. If successful, users will receive confirmation emails from `<EMAIL>`
3. Admin notifications will be sent to `<EMAIL>`
4. All form submissions will trigger both user and admin emails

## Files Updated
- `src/lib/email.ts` - Main email configuration
- Password changed from `Honda008!` to `Chasemia2015!`
- Using SSL configuration (port 465)
