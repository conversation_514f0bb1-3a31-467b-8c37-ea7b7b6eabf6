import nodemailer from 'nodemailer';

// Create transporter using environment variables
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.EMAIL_SMTP_HOST,
    port: parseInt(process.env.EMAIL_SMTP_PORT || '465'),
    secure: process.env.EMAIL_SMTP_SECURE === 'true',
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASSWORD
    },
    tls: {
      rejectUnauthorized: false
    }
  });
};

// Send form submission confirmation email
export const sendFormSubmissionEmail = async (
  userEmail: string,
  userName: string,
  vehicleName: string,
  submissionId: string
) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: userEmail,
      subject: 'Form Submission Confirmation - Premium Rust Prevention',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1f2937, #374151); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
            .highlight { color: #d97706; font-weight: bold; }
            .footer { text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px; }
            .button { display: inline-block; background: #d97706; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Premium Rust Prevention</h1>
              <h2>Form Submission Confirmed!</h2>
            </div>
            <div class="content">
              <p>Dear <strong>${userName}</strong>,</p>

              <p>Thank you for submitting your vehicle form for <span class="highlight">${vehicleName}</span>. We have successfully received your submission and our team will review it shortly.</p>

              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d97706;">
                <h3>Submission Details:</h3>
                <p><strong>Submission ID:</strong> ${submissionId}</p>
                <p><strong>Vehicle:</strong> ${vehicleName}</p>
                <p><strong>Status:</strong> Pending Review</p>
                <p><strong>Submitted:</strong> ${new Date().toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</p>
              </div>

              <h3>What's Next?</h3>
              <ul>
                <li>Our team will review your submission within 24-48 hours</li>
                <li>You'll receive an email notification once the review is complete</li>
                <li>You can track your submission status in your dashboard</li>
              </ul>
              
              <p>If you have any questions or need immediate assistance, please don't hesitate to contact our support team.</p>
              
              <p>Thank you for choosing Premium Rust Prevention!</p>
              
              <p>Best regards,<br>
              <strong>The Premium Rust Prevention Team</strong></p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
              <p>© ${new Date().getFullYear()} Premium Rust Prevention. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Form submission email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending form submission email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Send password reset code email
export const sendPasswordResetEmail = async (
  userEmail: string,
  resetCode: string
) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: userEmail,
      subject: 'Password Reset Code - Premium Rust Prevention',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1f2937, #374151); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
            .code-box { background: white; border: 2px solid #d97706; padding: 20px; text-align: center; border-radius: 8px; margin: 20px 0; }
            .code { font-size: 32px; font-weight: bold; color: #d97706; letter-spacing: 8px; }
            .footer { text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px; }
            .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Premium Rust Prevention</h1>
              <h2>Password Reset Request</h2>
            </div>
            <div class="content">
              <p>Hello,</p>
              
              <p>We received a request to reset your password. Use the 4-digit code below to proceed with resetting your password:</p>
              
              <div class="code-box">
                <p style="margin: 0; font-size: 16px; color: #6b7280;">Your Reset Code:</p>
                <div class="code">${resetCode}</div>
              </div>
              
              <div class="warning">
                <p><strong>Important Security Information:</strong></p>
                <ul style="margin: 10px 0;">
                  <li>This code will expire in 15 minutes</li>
                  <li>Do not share this code with anyone</li>
                  <li>If you didn't request this reset, please ignore this email</li>
                </ul>
              </div>
              
              <p>Enter this code on the password reset page to continue. If you didn't request a password reset, you can safely ignore this email.</p>
              
              <p>For security reasons, this code will expire in 15 minutes.</p>
              
              <p>Best regards,<br>
              <strong>The Premium Rust Prevention Team</strong></p>
            </div>
            <div class="footer">
              <p>This is an automated message. Please do not reply to this email.</p>
              <p>© ${new Date().getFullYear()} Premium Rust Prevention. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Password reset email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending password reset email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Send admin notification email when a form is submitted
export const sendAdminNotificationEmail = async (
  salesmanName: string,
  salesmanEmail: string,
  dealership: string,
  vehicleName: string,
  vehicleId: string,
  totalCarValue: number,
  prpSales: number,
  submissionId: string
) => {
  try {
    const transporter = createTransporter();
    const adminEmail = process.env.ADMIN_EMAIL;

    const mailOptions = {
      from: process.env.EMAIL_FROM,
      to: adminEmail,
      subject: `New Form Submission - ${vehicleName} by ${salesmanName}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1f2937, #374151); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
            .content { background: #f9fafb; padding: 30px; border-radius: 0 0 10px 10px; }
            .highlight { color: #d97706; font-weight: bold; }
            .info-box { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #d97706; }
            .footer { text-align: center; margin-top: 20px; color: #6b7280; font-size: 14px; }
            .button { display: inline-block; background: #d97706; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
            .urgent { background: #fef2f2; border: 1px solid #fca5a5; padding: 15px; border-radius: 6px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>Premium Rust Prevention</h1>
              <h2>New Form Submission Alert</h2>
            </div>
            <div class="content">
              <div class="urgent">
                <p><strong>Admin Alert:</strong> A new form submission from <strong>${salesmanName}</strong> requires your review!</p>
              </div>

              <div class="info-box">
                <h3>Submission Details:</h3>
                <p><strong>Submission ID:</strong> ${submissionId}</p>
                <p><strong>Vehicle:</strong> ${vehicleName}</p>
                <p><strong>Vehicle ID:</strong> ${vehicleId}</p>
                <p><strong>Total Car Value:</strong> <span class="highlight">$${totalCarValue.toLocaleString()}</span></p>
                <p><strong>PRP Commission:</strong> <span class="highlight">$${prpSales.toLocaleString()}</span></p>
                <p><strong>Status:</strong> Pending Review</p>
                <p><strong>Submitted:</strong> ${new Date().toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}</p>
              </div>

              <div class="info-box">
                <h3>Salesman Information:</h3>
                <p><strong>Salesman Name:</strong> ${salesmanName}</p>
                <p><strong>Salesman Email:</strong> ${salesmanEmail}</p>
                <p><strong>Dealership:</strong> ${dealership}</p>
              </div>

              <h3>Next Steps:</h3>
              <ul>
                <li>Review the submission details in the admin panel</li>
                <li>Approve or reject the submission</li>
                <li>The salesman will be notified of your decision</li>
              </ul>

              <div style="text-align: center; margin: 30px 0;">
                <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/admin/forms" class="button">
                  Review in Admin Panel
                </a>
              </div>

              <p>This submission is now available in your admin dashboard for review and action.</p>

              <p>Best regards,<br>
              <strong>Premium Rust Prevention System</strong></p>
            </div>
            <div class="footer">
              <p>This is an automated admin notification. Please review promptly.</p>
              <p>© ${new Date().getFullYear()} Premium Rust Prevention. All rights reserved.</p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Admin notification email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };
  } catch (error) {
    console.error('Error sending admin notification email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};

// Test email configuration
export const testEmailConnection = async () => {
  try {
    const transporter = createTransporter();
    await transporter.verify();
    console.log('Email configuration is valid');
    return { success: true };
  } catch (error) {
    console.error('Email configuration error:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
};
