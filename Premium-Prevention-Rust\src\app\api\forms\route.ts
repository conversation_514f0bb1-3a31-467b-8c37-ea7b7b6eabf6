/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import FormSubmission from '@/models/FormSubmission';
import User from '@/models/User';
import { verifyToken } from '@/lib/auth';
import { sendFormSubmissionEmail, sendAdminNotificationEmail } from '@/lib/email';
import { uploadToCloudinary } from '@/lib/cloudinary';
import fs from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.replace('Bearer ', '');

    // If no token in header, try cookies
    if (!token) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);
        token = cookies['auth_token'];
      }
    }

    // If still no token, try .env file as fallback
    if (!token) {
      try {
        const envPath = path.join(process.cwd(), '.env.local');
        const envContent = fs.readFileSync(envPath, 'utf8');
        const tokenLine = envContent.split('\n').find(line => line.startsWith('USER_TOKEN='));
        if (tokenLine) {
          token = tokenLine.split('=')[1];
        }
      } catch (error) {
        // No token found
      }
    }

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided. Please log in again.' },
        { status: 401 }
      );
    }

    // Verify token and extract user ID
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token. Please log in again.' },
        { status: 401 }
      );
    }

    const userId = payload.userId;

    const formData = await request.formData();
    
    // Extract form fields
    const vehicleName = formData.get('vehicleName') as string;
    const vehicleId = formData.get('vehicleId') as string;
    const totalCarValue = parseFloat(formData.get('totalCarValue') as string);
    const prpSales = parseFloat(formData.get('prpSales') as string);
    const date = formData.get('date') as string;
    
    // Optional fields
    const customerName = formData.get('customerName') as string || undefined;
    const customerPhone = formData.get('customerPhone') as string || undefined;
    const customerEmail = formData.get('customerEmail') as string || undefined;
    const vehicleModel = formData.get('vehicleModel') as string || undefined;
    const vehicleYear = formData.get('vehicleYear') ? parseInt(formData.get('vehicleYear') as string) : undefined;
    const vehicleColor = formData.get('vehicleColor') as string || undefined;
    const mileage = formData.get('mileage') ? parseFloat(formData.get('mileage') as string) : undefined;
    const condition = formData.get('condition') as string || 'good';
    const notes = formData.get('notes') as string || undefined;

    // Validation
    if (!vehicleName || !vehicleId || !totalCarValue || !prpSales || !date) {
      return NextResponse.json(
        { error: 'Vehicle name, ID, total car value, PRP sales, and date are required' },
        { status: 400 }
      );
    }

    if (totalCarValue <= 0 || prpSales <= 0) {
      return NextResponse.json(
        { error: 'Total car value and PRP sales must be greater than 0' },
        { status: 400 }
      );
    }

    // Handle file uploads to Cloudinary
    const pictures: string[] = [];

    // Process uploaded pictures
    for (const [key, value] of formData.entries()) {
      if (key.startsWith('picture_') && value instanceof File) {
        const file = value as File;
        if (file.size > 0) {
          try {
            // Convert file to base64 for Cloudinary upload
            const bytes = await file.arrayBuffer();
            const buffer = Buffer.from(bytes);
            const base64 = `data:${file.type};base64,${buffer.toString('base64')}`;

            // Upload to Cloudinary
            const uploadResult = await uploadToCloudinary(base64, {
              folder: 'premium-rust-prevention/vehicles',
              resource_type: 'image',
            });

            pictures.push(uploadResult.secure_url);
          } catch (uploadError) {
            console.error('Failed to upload image to Cloudinary:', uploadError);
            // Continue with other images even if one fails
          }
        }
      }
    }

    // Create form submission
    const submission = new FormSubmission({
      userId,
      vehicleName: vehicleName.trim(),
      vehicleId: vehicleId.trim(),
      pictures,
      timestamp: new Date(),
      date: new Date(date),
      totalCarValue,
      prpSales,
      customerName: customerName?.trim(),
      customerPhone: customerPhone?.trim(),
      customerEmail: customerEmail?.trim(),
      vehicleModel: vehicleModel?.trim(),
      vehicleYear,
      vehicleColor: vehicleColor?.trim(),
      mileage,
      condition,
      notes: notes?.trim(),
      status: 'pending'
    });

    await submission.save();

    // Send confirmation email to user
    try {
      const emailResult = await sendFormSubmissionEmail(
        payload.email,
        customerName || 'Valued Customer',
        vehicleName,
        submission._id.toString()
      );

      if (emailResult.success) {
        console.log('Confirmation email sent successfully');
      } else {
        console.error('Failed to send confirmation email:', emailResult.error);
      }
    } catch (emailError) {
      console.error('Email sending error:', emailError);
      // Don't fail the form submission if email fails
    }

    // Send admin notification email
    try {
      console.log('Attempting to send admin notification email...');

      // Retrieve full user information from database for email
      const user = await User.findById(payload.userId).select('name email dealership');
      if (!user) {
        console.error('❌ User not found for admin email notification');
        throw new Error('User not found');
      }

      console.log('📧 Sending admin email with salesman info:', {
        name: user.name,
        email: user.email,
        dealership: user.dealership
      });

      const adminEmailResult = await sendAdminNotificationEmail(
        user.name || 'Unknown Salesman',
        user.email,
        user.dealership || 'Unknown Dealership',
        vehicleName,
        vehicleId,
        totalCarValue,
        prpSales,
        submission._id.toString()
      );

      if (adminEmailResult.success) {
        console.log('✅ Admin notification email sent <NAME_EMAIL>');
        console.log('Message ID:', adminEmailResult.messageId);
        console.log('📧 Email included salesman details:', {
          name: user.name,
          email: user.email,
          dealership: user.dealership
        });
      } else {
        console.error('❌ Failed to send admin notification email:', adminEmailResult.error);
      }
    } catch (adminEmailError) {
      console.error('❌ Admin email sending error:', adminEmailError);
      // Don't fail the form submission if admin email fails
    }

    return NextResponse.json(
      {
        message: 'Form submitted successfully',
        submission: {
          id: submission._id,
          vehicleName: submission.vehicleName,
          vehicleId: submission.vehicleId,
          totalCarValue: submission.totalCarValue,
          prpSales: submission.prpSales,
          status: submission.status,
          createdAt: submission.createdAt
        }
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Form submission error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    let token = authHeader?.replace('Bearer ', '');

    // If no token in header, try cookies
    if (!token) {
      const cookieHeader = request.headers.get('cookie');
      if (cookieHeader) {
        const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          acc[key] = value;
          return acc;
        }, {} as Record<string, string>);
        token = cookies['auth_token'];
      }
    }

    // If still no token, try .env file as fallback
    if (!token) {
      try {
        const envPath = path.join(process.cwd(), '.env.local');
        const envContent = fs.readFileSync(envPath, 'utf8');
        const tokenLine = envContent.split('\n').find(line => line.startsWith('USER_TOKEN='));
        if (tokenLine) {
          token = tokenLine.split('=')[1];
        }
      } catch (error) {
        // No token found
      }
    }

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    // Verify token and extract user ID
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json(
        { error: 'Unauthorized - Invalid token' },
        { status: 401 }
      );
    }

    const userId = payload.userId;

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const status = searchParams.get('status');

    const skip = (page - 1) * limit;

    // Build query
    const query: any = { userId };
    if (status && ['pending', 'approved', 'rejected'].includes(status)) {
      query.status = status;
    }

    // Get submissions with pagination
    const submissions = await FormSubmission.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .select('-__v');

    const total = await FormSubmission.countDocuments(query);

    return NextResponse.json(
      {
        submissions,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Get submissions error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
