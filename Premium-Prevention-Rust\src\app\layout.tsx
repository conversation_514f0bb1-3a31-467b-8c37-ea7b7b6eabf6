import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Premium Rust Prevention - Professional Vehicle Protection",
  description: "Premium Rust Prevention (PRP) provides comprehensive rust prevention solutions for automotive dealerships and salespeople. Track your sales, manage submissions, and protect vehicles with our advanced coating technology.",
  keywords: "rust prevention, vehicle protection, automotive, dealership, car protection, PRP",
  icons: {
    icon: [
      {
        url: "/PRP LOGO.jpg",
        sizes: "32x32",
        type: "image/jpeg",
      },
      {
        url: "/PRP LOGO.jpg",
        sizes: "16x16",
        type: "image/jpeg",
      },
    ],
    shortcut: "/PRP LOGO.jpg",
    apple: {
      url: "/PRP LOGO.jpg",
      sizes: "180x180",
      type: "image/jpeg",
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" href="/PRP LOGO.jpg?v=4" type="image/jpeg" sizes="32x32" />
        <link rel="icon" href="/PRP LOGO.jpg?v=4" type="image/jpeg" sizes="16x16" />
        <link rel="shortcut icon" href="/PRP LOGO.jpg?v=4" type="image/jpeg" />
        <link rel="apple-touch-icon" href="/PRP LOGO.jpg?v=4" sizes="180x180" />
        <link rel="apple-touch-icon-precomposed" href="/PRP LOGO.jpg?v=4" />
        <meta name="msapplication-TileImage" content="/PRP LOGO.jpg?v=4" />
        <script dangerouslySetInnerHTML={{
          __html: `
            // Force favicon refresh
            (function() {
              var links = document.querySelectorAll("link[rel*='icon']");
              links.forEach(function(link) {
                var href = link.href;
                link.href = href + (href.indexOf('?') !== -1 ? '&' : '?') + 'v=' + Date.now();
              });
            })();
          `
        }} />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
