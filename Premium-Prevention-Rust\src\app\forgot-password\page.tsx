'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Mail, Lock, ArrowLeft, Shield } from 'lucide-react';
import Swal from 'sweetalert2';

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [step, setStep] = useState<'email' | 'code' | 'password'>('email');
  const [email, setEmail] = useState('');
  const [resetCode, setResetCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      await Swal.fire({
        title: 'Error!',
        text: 'Please enter your email address',
        icon: 'error',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (response.ok) {
        await Swal.fire({
          title: 'Email Sent!',
          text: data.message,
          icon: 'success',
          confirmButtonColor: '#10b981',
          background: '#1f2937',
          color: '#f3f4f6',
        });
        setStep('code');
      } else {
        await Swal.fire({
          title: 'Error!',
          text: data.error || 'Failed to send reset email',
          icon: 'error',
          confirmButtonColor: '#ef4444',
          background: '#1f2937',
          color: '#f3f4f6',
        });
      }
    } catch (error) {
      await Swal.fire({
        title: 'Error!',
        text: 'Network error. Please try again.',
        icon: 'error',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCodeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!resetCode || resetCode.length !== 4) {
      await Swal.fire({
        title: 'Error!',
        text: 'Please enter the 4-digit code',
        icon: 'error',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
      });
      return;
    }

    setStep('password');
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newPassword || newPassword.length < 6) {
      await Swal.fire({
        title: 'Error!',
        text: 'Password must be at least 6 characters long',
        icon: 'error',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
      });
      return;
    }

    if (newPassword !== confirmPassword) {
      await Swal.fire({
        title: 'Error!',
        text: 'Passwords do not match',
        icon: 'error',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, resetCode, newPassword }),
      });

      const data = await response.json();

      if (response.ok) {
        await Swal.fire({
          title: 'Success!',
          text: data.message,
          icon: 'success',
          confirmButtonColor: '#10b981',
          background: '#1f2937',
          color: '#f3f4f6',
        });
        router.push('/login');
      } else {
        await Swal.fire({
          title: 'Error!',
          text: data.error || 'Failed to reset password',
          icon: 'error',
          confirmButtonColor: '#ef4444',
          background: '#1f2937',
          color: '#f3f4f6',
        });
      }
    } catch (error) {
      await Swal.fire({
        title: 'Error!',
        text: 'Network error. Please try again.',
        icon: 'error',
        confirmButtonColor: '#ef4444',
        background: '#1f2937',
        color: '#f3f4f6',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-2xl p-8 shadow-2xl backdrop-blur-sm">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Shield className="w-12 h-12 text-prp-gold" />
            </div>
            <h1 className="text-3xl font-bold text-prp-light mb-2">
              {step === 'email' && 'Forgot Password'}
              {step === 'code' && 'Enter Reset Code'}
              {step === 'password' && 'Set New Password'}
            </h1>
            <p className="text-prp-silver">
              {step === 'email' && 'Enter your email to receive a reset code'}
              {step === 'code' && 'Check your email for the 4-digit code'}
              {step === 'password' && 'Create your new password'}
            </p>
          </div>

          {/* Step 1: Email Input */}
          {step === 'email' && (
            <form onSubmit={handleEmailSubmit} className="space-y-6">
              <div>
                <label className="block text-prp-silver text-sm font-medium mb-2">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver/50" />
                  <Input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <Button
                type="submit"
                variant="primary"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? 'Sending...' : 'Send Reset Code'}
              </Button>
            </form>
          )}

          {/* Step 2: Code Input */}
          {step === 'code' && (
            <form onSubmit={handleCodeSubmit} className="space-y-6">
              <div>
                <label className="block text-prp-silver text-sm font-medium mb-2">
                  4-Digit Reset Code
                </label>
                <Input
                  type="text"
                  value={resetCode}
                  onChange={(e) => setResetCode(e.target.value.replace(/\D/g, '').slice(0, 4))}
                  placeholder="Enter 4-digit code"
                  className="text-center text-2xl tracking-widest"
                  maxLength={4}
                  required
                />
                <p className="text-prp-silver/70 text-xs mt-2">
                  Code sent to: {email}
                </p>
              </div>

              <Button
                type="submit"
                variant="primary"
                className="w-full"
                disabled={resetCode.length !== 4}
              >
                Verify Code
              </Button>
            </form>
          )}

          {/* Step 3: New Password */}
          {step === 'password' && (
            <form onSubmit={handlePasswordSubmit} className="space-y-6">
              <div>
                <label className="block text-prp-silver text-sm font-medium mb-2">
                  New Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver/50" />
                  <Input
                    type="password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    placeholder="Enter new password"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-prp-silver text-sm font-medium mb-2">
                  Confirm New Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver/50" />
                  <Input
                    type="password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    placeholder="Confirm new password"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              <Button
                type="submit"
                variant="primary"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? 'Updating...' : 'Update Password'}
              </Button>
            </form>
          )}

          {/* Back to Login */}
          <div className="mt-6 text-center">
            <button
              onClick={() => router.push('/login')}
              className="inline-flex items-center text-prp-gold hover:text-prp-light transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Login
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
