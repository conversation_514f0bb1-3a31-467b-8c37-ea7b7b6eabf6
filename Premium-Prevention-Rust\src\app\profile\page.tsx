'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { useAuth } from '@/hooks/useAuth';
import { useProfileImage } from '@/hooks/useProfileImage';
import {
  User,
  Camera,
  Edit3,
  Save,
  X,
  Mail,
  Phone,
  Building,
  Calendar,
  FileText,
  DollarSign,
  TrendingUp,
  Award
} from 'lucide-react';
import { formatCurrency, formatDateTime } from '@/lib/utils';

// Animated Counter Component
const AnimatedCounter: React.FC<{ end: number; duration?: number; delay?: number; prefix?: string; suffix?: string }> = ({
  end,
  duration = 2000,
  delay = 0,
  prefix = '',
  suffix = ''
}) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasStarted(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (!hasStarted) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, hasStarted]);

  return <span>{prefix}{count.toLocaleString()}{suffix}</span>;
};

interface UserStats {
  totalSubmissions: number;
  totalRevenue: number;
  highestPrpSale: number;
  averageCarValue: number;
  pendingSubmissions: number;
  approvedSubmissions: number;
  rejectedSubmissions: number;
}

export default function ProfilePage() {
  const router = useRouter();
  const { user, updateUser } = useAuth();
  const { profileImage, updateProfileImage } = useProfileImage();
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  const [editForm, setEditForm] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
    dealership: user?.dealership || ''
  });

  useEffect(() => {
    loadUserStats();
    loadProfileImage();
  }, []);

  // Trigger animations after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const loadUserStats = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to load user stats:', error);
    }
  };

  const loadProfileImage = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/profile-image', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.profileImage) {
          updateProfileImage(data.profileImage);
        }
      }
    } catch (error) {
      console.error('Failed to load profile image:', error);
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size should be less than 5MB');
      return;
    }

    setIsUploadingImage(true);

    try {
      const formData = new FormData();
      formData.append('profileImage', file);

      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/profile-image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        updateProfileImage(data.profileImage);
      } else {
        alert('Failed to upload image');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      alert('Failed to upload image');
    } finally {
      setIsUploadingImage(false);
    }
  };

  const handleSaveProfile = async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/profile', {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (response.ok) {
        const data = await response.json();
        updateUser(data.user);
        setIsEditing(false);
      } else {
        alert('Failed to update profile');
      }
    } catch (error) {
      console.error('Profile update error:', error);
      alert('Failed to update profile');
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-prp-gold mx-auto mb-4"></div>
          <p className="text-prp-silver">Loading profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
      <Header />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className={`mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center">
            <User className="w-10 h-10 mr-3 text-prp-gold" />
            My Profile
          </h1>
          <p className="text-prp-silver">
            Manage your account information and view your performance statistics.
          </p>
        </div>

        <div className="space-y-8">
          {/* Profile Information Container */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-8 shadow-2xl transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '200ms' }}>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-prp-light">Profile Information</h2>
                {!isEditing ? (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="flex items-center space-x-2"
                  >
                    <Edit3 className="w-4 h-4" />
                    <span>Edit</span>
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      variant="primary"
                      size="sm"
                      onClick={handleSaveProfile}
                      loading={isLoading}
                      className="flex items-center space-x-2"
                    >
                      <Save className="w-4 h-4" />
                      <span>Save</span>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setIsEditing(false);
                        setEditForm({
                          name: user.name,
                          phone: user.phone || '',
                          dealership: user.dealership || ''
                        });
                      }}
                      className="flex items-center space-x-2"
                    >
                      <X className="w-4 h-4" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                )}
              </div>

              <div className="flex flex-col md:flex-row md:items-start space-y-6 md:space-y-0 md:space-x-8">
                {/* Profile Picture */}
                <div className="flex flex-col items-center space-y-4">
                  <div className="relative">
                    <div className="w-32 h-32 rounded-full overflow-hidden bg-prp-gold/20 border-4 border-prp-gold/30">
                      {profileImage ? (
                        <img
                          src={profileImage}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <User className="w-16 h-16 text-prp-gold" />
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      disabled={isUploadingImage}
                      className="absolute bottom-0 right-0 bg-prp-gold hover:bg-prp-gold/80 text-prp-dark p-2 rounded-full transition-colors duration-200 disabled:opacity-50"
                    >
                      {isUploadingImage ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-prp-dark"></div>
                      ) : (
                        <Camera className="w-4 h-4" />
                      )}
                    </button>
                  </div>
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                  <p className="text-prp-silver text-sm text-center">
                    Click camera icon to change photo
                  </p>
                </div>

                {/* Profile Details */}
                <div className="flex-1 space-y-6">
                  {isEditing ? (
                    <div className="space-y-4">
                      <Input
                        label="Full Name"
                        value={editForm.name}
                        onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                        placeholder="Enter your full name"
                      />
                      <Input
                        label="Phone Number"
                        value={editForm.phone}
                        onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                        placeholder="Enter your phone number"
                      />
                      <Input
                        label="Dealership"
                        value={editForm.dealership}
                        onChange={(e) => setEditForm({ ...editForm, dealership: e.target.value })}
                        placeholder="Enter your dealership name"
                      />
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="flex items-center space-x-3">
                        <User className="w-5 h-5 text-prp-gold" />
                        <div>
                          <p className="text-prp-light font-medium">{user.name}</p>
                          <p className="text-prp-silver text-sm">Full Name</p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-3">
                        <Mail className="w-5 h-5 text-prp-gold" />
                        <div>
                          <p className="text-prp-light font-medium">{user.email}</p>
                          <p className="text-prp-silver text-sm">Email Address</p>
                        </div>
                      </div>

                      {user.phone && (
                        <div className="flex items-center space-x-3">
                          <Phone className="w-5 h-5 text-prp-gold" />
                          <div>
                            <p className="text-prp-light font-medium">{user.phone}</p>
                            <p className="text-prp-silver text-sm">Phone Number</p>
                          </div>
                        </div>
                      )}

                      {user.dealership && (
                        <div className="flex items-center space-x-3">
                          <Building className="w-5 h-5 text-prp-gold" />
                          <div>
                            <p className="text-prp-light font-medium">{user.dealership}</p>
                            <p className="text-prp-silver text-sm">Dealership</p>
                          </div>
                        </div>
                      )}

                      <div className="flex items-center space-x-3">
                        <Calendar className="w-5 h-5 text-prp-gold" />
                        <div>
                          <p className="text-prp-light font-medium">
                            {formatDateTime(user.createdAt)}
                          </p>
                          <p className="text-prp-silver text-sm">Member Since</p>
                        </div>
                      </div>

                      <div className="inline-flex items-center space-x-2 bg-prp-gold/20 border border-prp-gold/40 rounded-full px-4 py-2">
                        <Award className="w-4 h-4 text-prp-gold" />
                        <span className="text-prp-gold font-medium capitalize">{user.role}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

          {/* Performance Stats Container */}
          <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-8 shadow-2xl transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
               style={{ transitionDelay: '400ms' }}>
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <TrendingUp className="w-6 h-6 mr-2 text-prp-gold" />
              Performance Stats
            </h3>

            {stats ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Total Forms */}
                <div className={`bg-prp-dark/40 rounded-lg p-6 text-center hover:bg-prp-dark/60 transition-all duration-300 hover:scale-105 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                     style={{ transitionDelay: '600ms' }}>
                  <div className="flex items-center justify-center mb-4">
                    <div className="p-3 bg-blue-500/20 rounded-lg">
                      <FileText className="w-6 h-6 text-blue-400" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-prp-light mb-2">
                    <AnimatedCounter end={stats.totalSubmissions} delay={800} />
                  </div>
                  <p className="text-prp-silver text-sm">Total Forms</p>
                </div>

                {/* Approved */}
                <div className={`bg-prp-dark/40 rounded-lg p-6 text-center hover:bg-prp-dark/60 transition-all duration-300 hover:scale-105 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                     style={{ transitionDelay: '700ms' }}>
                  <div className="flex items-center justify-center mb-4">
                    <div className="p-3 bg-green-500/20 rounded-lg">
                      <Award className="w-6 h-6 text-green-400" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-prp-light mb-2">
                    <AnimatedCounter end={stats.approvedSubmissions} delay={900} />
                  </div>
                  <p className="text-prp-silver text-sm">Approved</p>
                </div>

                {/* Revenue */}
                <div className={`bg-prp-dark/40 rounded-lg p-6 text-center hover:bg-prp-dark/60 transition-all duration-300 hover:scale-105 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                     style={{ transitionDelay: '800ms' }}>
                  <div className="flex items-center justify-center mb-4">
                    <div className="p-3 bg-prp-gold/20 rounded-lg">
                      <DollarSign className="w-6 h-6 text-prp-gold" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-prp-light mb-2">
                    $<AnimatedCounter end={stats.totalRevenue} delay={1000} />
                  </div>
                  <p className="text-prp-silver text-sm">Revenue</p>
                </div>

                {/* Success Rate */}
                <div className={`bg-prp-dark/40 rounded-lg p-6 text-center hover:bg-prp-dark/60 transition-all duration-300 hover:scale-105 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
                     style={{ transitionDelay: '900ms' }}>
                  <div className="flex items-center justify-center mb-4">
                    <div className="p-3 bg-purple-500/20 rounded-lg">
                      <TrendingUp className="w-6 h-6 text-purple-400" />
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-prp-light mb-2">
                    <AnimatedCounter
                      end={stats.totalSubmissions > 0 ? Math.round((stats.approvedSubmissions / stats.totalSubmissions) * 100) : 0}
                      delay={1100}
                      suffix="%"
                    />
                  </div>
                  <p className="text-prp-silver text-sm">Success Rate</p>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-prp-gold mx-auto mb-4"></div>
                <p className="text-prp-silver animate-pulse">Loading stats...</p>
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}