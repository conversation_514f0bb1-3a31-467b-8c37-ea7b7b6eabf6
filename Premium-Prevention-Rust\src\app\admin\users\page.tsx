'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AdminHeader from '@/components/AdminHeader';
import {
  Users,
  Search,
  Building,
  Mail,
  Phone,
  Calendar,
  UserCheck,
  UserX
} from 'lucide-react';
import { formatDateTime } from '@/lib/utils';

interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  dealership?: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  submissionCount?: number;
  totalRevenue?: number;
}

export default function AdminUsersPage() {
  const router = useRouter();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Check admin authentication
    const token = localStorage.getItem('admin_token');
    const adminInfo = localStorage.getItem('admin_data');
    
    if (!token || !adminInfo) {
      router.push('/admin/login');
      return;
    }

    try {
      const admin = JSON.parse(adminInfo);
      if (admin.role !== 'admin') {
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      router.push('/admin/login');
      return;
    }

    loadUsers();
  }, [router]);

  // Trigger animations after data loads
  useEffect(() => {
    if (users.length > 0 && !isLoading) {
      setIsVisible(true);
    }
  }, [users, isLoading]);

  const loadUsers = async () => {
    try {
      setIsLoading(true);
      const token = localStorage.getItem('admin_token');
      
      const response = await fetch('/api/admin/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (error) {
      console.error('Failed to load users:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.dealership?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleLogout = () => {
    localStorage.removeItem('admin_token');
    localStorage.removeItem('admin_data');
    router.push('/admin/login');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
        <AdminHeader onLogout={handleLogout} currentPage="users" />
        <div className="flex items-center justify-center py-20">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-prp-gold mx-auto mb-4"></div>
            <p className="text-prp-silver animate-pulse">Loading users...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark relative overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-prp-gold/5 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-green-500/5 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <AdminHeader onLogout={handleLogout} currentPage="users" />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 relative z-10">
        {/* Header */}
        <div className={`mb-8 transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
          <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center group">
            <Users className="w-10 h-10 mr-3 text-prp-gold transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12" />
            <span className="bg-gradient-to-r from-prp-light to-prp-gold bg-clip-text text-transparent">
              User Management
            </span>
          </h1>
          <p className="text-prp-silver text-lg">
            Manage all registered salesmen and their account details.
          </p>
        </div>

        {/* Search */}
        <div className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 mb-8 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
             style={{ transitionDelay: '200ms' }}>
          <div className="relative max-w-md group">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver group-focus-within:text-prp-gold transition-colors duration-300" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full bg-prp-dark border border-prp-silver/30 text-prp-light rounded-lg pl-10 pr-4 py-2 focus:outline-none focus:ring-2 focus:ring-prp-gold focus:border-prp-gold/50 transition-all duration-300 hover:border-prp-gold/30 hover:scale-105"
            />
          </div>
        </div>

        {/* Users Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredUsers.map((user, index) => (
            <div
              key={user.id}
              className={`bg-prp-dark/60 backdrop-blur-sm border border-prp-silver/30 rounded-xl p-6 shadow-lg hover:shadow-prp-gold/20 transition-all duration-500 hover:scale-105 hover:border-prp-gold/50 group ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}
              style={{ transitionDelay: `${400 + index * 100}ms` }}
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-prp-gold/20 rounded-lg group-hover:bg-prp-gold/30 transition-all duration-300 group-hover:scale-110">
                    <Users className="w-6 h-6 text-prp-gold group-hover:text-yellow-300 transition-colors duration-300" />
                  </div>
                  <div>
                    <h3 className="text-prp-light font-bold group-hover:text-prp-gold transition-colors duration-300">{user.name}</h3>
                    <p className="text-prp-silver text-sm group-hover:text-prp-light transition-colors duration-300">{user.role}</p>
                  </div>
                </div>
                <div className={`p-2 rounded-full transition-all duration-300 hover:scale-125 ${user.isActive ? 'bg-green-500/20 hover:bg-green-500/30' : 'bg-red-500/20 hover:bg-red-500/30'}`}>
                  {user.isActive ? (
                    <UserCheck className="w-4 h-4 text-green-400 hover:text-green-300 transition-colors duration-300" />
                  ) : (
                    <UserX className="w-4 h-4 text-red-400 hover:text-red-300 transition-colors duration-300" />
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Mail className="w-4 h-4 text-prp-silver" />
                  <span className="text-prp-light text-sm">{user.email}</span>
                </div>

                {user.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="w-4 h-4 text-prp-silver" />
                    <span className="text-prp-light text-sm">{user.phone}</span>
                  </div>
                )}

                {user.dealership && (
                  <div className="flex items-center space-x-2">
                    <Building className="w-4 h-4 text-prp-silver" />
                    <span className="text-prp-light text-sm">{user.dealership}</span>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Calendar className="w-4 h-4 text-prp-silver" />
                  <span className="text-prp-silver text-sm">
                    Joined {formatDateTime(user.createdAt)}
                  </span>
                </div>
              </div>

              {(user.submissionCount !== undefined || user.totalRevenue !== undefined) && (
                <div className="mt-4 pt-4 border-t border-prp-silver/20">
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div>
                      <p className="text-prp-gold font-bold text-lg">{user.submissionCount || 0}</p>
                      <p className="text-prp-silver text-xs">Submissions</p>
                    </div>
                    <div>
                      <p className="text-prp-gold font-bold text-lg">
                        ${(user.totalRevenue || 0).toLocaleString()}
                      </p>
                      <p className="text-prp-silver text-xs">Revenue</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredUsers.length === 0 && (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-prp-silver mx-auto mb-4" />
            <h3 className="text-xl font-bold text-prp-light mb-2">No Users Found</h3>
            <p className="text-prp-silver">
              {searchTerm ? 'No users match your search criteria.' : 'No users have registered yet.'}
            </p>
          </div>
        )}
      </main>
    </div>
  );
}
