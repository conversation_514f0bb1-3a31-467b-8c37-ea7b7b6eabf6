import { NextRequest, NextResponse } from 'next/server';
import { sendAdminNotificationEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    // Test admin notification email
    const result = await sendAdminNotificationEmail(
      'Test Salesman',
      '<EMAIL>',
      'Test Dealership',
      'Test Vehicle',
      'TEST123',
      50000,
      2500,
      'test-submission-id'
    );
    
    if (!result.success) {
      return NextResponse.json(
        { 
          error: 'Failed to send admin notification email', 
          details: result.error 
        },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { 
        message: 'Admin notification email sent successfully!',
        messageId: result.messageId
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Admin email test error:', error);
    return NextResponse.json(
      { error: 'Admin email test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
