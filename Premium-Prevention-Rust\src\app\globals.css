@import "tailwindcss";

:root {
  --background: #0a0a0a;
  --foreground: #ededed;
  --prp-silver: #c0c0c0;
  --prp-gold: #d4af37;
  --prp-dark: #1a1a1a;
  --prp-light: #f5f5f5;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-prp-silver: var(--prp-silver);
  --color-prp-gold: var(--prp-gold);
  --color-prp-dark: var(--prp-dark);
  --color-prp-light: var(--prp-light);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

/* Smooth transitions for all elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* FAQ Animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--prp-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--prp-silver);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--prp-gold);
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes success-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-success-bounce {
  animation: success-bounce 1s ease-in-out;
}
