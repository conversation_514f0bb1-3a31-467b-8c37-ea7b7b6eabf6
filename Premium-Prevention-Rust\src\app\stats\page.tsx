'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Header from '@/components/Header';
import Button from '@/components/ui/Button';
import {
  BarChart3,
  TrendingUp,
  Car,
  DollarSign,
  Calendar,
  CheckCircle,
  Clock,
  XCircle,
  FileText,
  Users,
  Activity
} from 'lucide-react';

interface StatsData {
  totalSubmissions: number;
  totalCarValue: number;
  totalPrpSales: number;
  averageCarValue: number;
  averagePrpSales: number;
  statusBreakdown: {
    pending: number;
    approved: number;
    rejected: number;
  };
  conditionBreakdown: {
    excellent: number;
    good: number;
    fair: number;
    poor: number;
  };
  monthlyData: Array<{
    month: string;
    submissions: number;
    totalValue: number;
    prpSales: number;
  }>;
  recentSubmissions: Array<{
    id: string;
    vehicleName: string;
    vehicleId: string;
    totalCarValue: number;
    prpSales: number;
    status: string;
    createdAt: string;
    customerName?: string;
    vehicleModel?: string;
    vehicleYear?: number;
  }>;
}

// Animated Counter Component
const AnimatedCounter: React.FC<{ end: number; duration?: number; delay?: number; prefix?: string; suffix?: string }> = ({
  end,
  duration = 2000,
  delay = 0,
  prefix = '',
  suffix = ''
}) => {
  const [count, setCount] = useState(0);
  const [hasStarted, setHasStarted] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setHasStarted(true);
    }, delay);

    return () => clearTimeout(timer);
  }, [delay]);

  useEffect(() => {
    if (!hasStarted) return;

    let startTime: number;
    let animationFrame: number;

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / duration, 1);

      setCount(Math.floor(progress * end));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate);
      }
    };

    animationFrame = requestAnimationFrame(animate);
    return () => cancelAnimationFrame(animationFrame);
  }, [end, duration, hasStarted]);

  return <span>{prefix}{count.toLocaleString()}{suffix}</span>;
};

export default function StatsPage() {
  const router = useRouter();
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    fetchStats();
  }, []);

  // Trigger animations after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const fetchStats = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load statistics');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      router.push('/login');
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
        <Header onLogout={handleLogout} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-prp-gold mx-auto mb-4"></div>
              <p className="text-prp-silver text-lg">Loading statistics...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
        <Header onLogout={handleLogout} />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <p className="text-red-400 text-lg mb-4">{error}</p>
              <Button onClick={fetchStats} variant="primary">
                Try Again
              </Button>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark">
      <Header onLogout={handleLogout} />

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-prp-light mb-2 flex items-center">
            <BarChart3 className="w-10 h-10 mr-3 text-prp-gold" />
            Form Submission Statistics
          </h1>
          <p className="text-prp-silver">
            Comprehensive overview of your form submissions and performance metrics.
          </p>
        </div>

        {/* Main Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Total Submissions */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <FileText className="w-6 h-6 text-blue-400" />
              </div>
              <span className="text-2xl font-bold text-prp-light">
                <AnimatedCounter end={stats?.totalSubmissions || 0} delay={200} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Total Submissions</h3>
            <p className="text-xs text-prp-silver/70 mt-1">All time form submissions</p>
          </div>

          {/* Total Car Value */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <DollarSign className="w-6 h-6 text-green-400" />
              </div>
              <span className="text-2xl font-bold text-prp-light">
                $<AnimatedCounter end={stats?.totalCarValue || 0} delay={400} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Total Car Value</h3>
            <p className="text-xs text-prp-silver/70 mt-1">Combined value of all vehicles</p>
          </div>

          {/* Total PRP Sales */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-prp-gold/20 rounded-lg">
                <TrendingUp className="w-6 h-6 text-prp-gold" />
              </div>
              <span className="text-2xl font-bold text-prp-light">
                $<AnimatedCounter end={stats?.totalPrpSales || 0} delay={600} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Total PRP Sales</h3>
            <p className="text-xs text-prp-silver/70 mt-1">Total rust prevention sales</p>
          </div>

          {/* Average Car Value */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500/20 rounded-lg">
                <Car className="w-6 h-6 text-purple-400" />
              </div>
              <span className="text-2xl font-bold text-prp-light">
                $<AnimatedCounter end={Math.round(stats?.averageCarValue || 0)} delay={800} />
              </span>
            </div>
            <h3 className="text-prp-silver text-sm font-medium">Average Car Value</h3>
            <p className="text-xs text-prp-silver/70 mt-1">Average value per vehicle</p>
          </div>
        </div>

        {/* Status Breakdown and Monthly Chart */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Status Breakdown */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <Activity className="w-6 h-6 mr-2 text-prp-gold" />
              Submission Status
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-yellow-400 mr-3" />
                  <span className="text-prp-light font-medium">Pending</span>
                </div>
                <span className="text-2xl font-bold text-yellow-400">
                  <AnimatedCounter end={stats?.statusBreakdown.pending || 0} delay={1000} />
                </span>
              </div>
              <div className="flex items-center justify-between p-4 bg-green-500/10 border border-green-500/30 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-400 mr-3" />
                  <span className="text-prp-light font-medium">Approved</span>
                </div>
                <span className="text-2xl font-bold text-green-400">
                  <AnimatedCounter end={stats?.statusBreakdown.approved || 0} delay={1200} />
                </span>
              </div>
              <div className="flex items-center justify-between p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
                <div className="flex items-center">
                  <XCircle className="w-5 h-5 text-red-400 mr-3" />
                  <span className="text-prp-light font-medium">Rejected</span>
                </div>
                <span className="text-2xl font-bold text-red-400">
                  <AnimatedCounter end={stats?.statusBreakdown.rejected || 0} delay={1400} />
                </span>
              </div>
            </div>
          </div>

          {/* Monthly Submissions Chart */}
          <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6">
            <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
              <Calendar className="w-6 h-6 mr-2 text-prp-gold" />
              Monthly Submissions
            </h3>
            <div className="space-y-3">
              {stats?.monthlyData.slice(-6).map((month, index) => (
                <div key={month.month} className="flex items-center justify-between">
                  <span className="text-prp-silver text-sm font-medium">{month.month}</span>
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center">
                      <div
                        className="h-2 bg-prp-gold rounded-full mr-2"
                        style={{
                          width: `${Math.max(20, (month.submissions / Math.max(...(stats?.monthlyData.map(m => m.submissions) || [1]))) * 100)}px`
                        }}
                      ></div>
                      <span className="text-prp-light font-bold">{month.submissions}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Submissions */}
        <div className="bg-prp-dark/60 border border-prp-silver/30 rounded-xl p-6 mb-8">
          <h3 className="text-xl font-bold text-prp-light mb-6 flex items-center">
            <Users className="w-6 h-6 mr-2 text-prp-gold" />
            Recent Submissions
          </h3>
          {stats?.recentSubmissions && stats.recentSubmissions.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-prp-silver/20">
                    <th className="text-left text-prp-silver text-sm font-medium py-3">Vehicle</th>
                    <th className="text-left text-prp-silver text-sm font-medium py-3">Customer</th>
                    <th className="text-left text-prp-silver text-sm font-medium py-3">Value</th>
                    <th className="text-left text-prp-silver text-sm font-medium py-3">PRP Sales</th>
                    <th className="text-left text-prp-silver text-sm font-medium py-3">Status</th>
                    <th className="text-left text-prp-silver text-sm font-medium py-3">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.recentSubmissions.map((submission) => (
                    <tr key={submission.id} className="border-b border-prp-silver/10 hover:bg-prp-silver/5">
                      <td className="py-4">
                        <div>
                          <p className="text-prp-light font-medium">{submission.vehicleName}</p>
                          <p className="text-prp-silver/70 text-sm">
                            {submission.vehicleModel} {submission.vehicleYear ? `(${submission.vehicleYear})` : ''}
                          </p>
                        </div>
                      </td>
                      <td className="py-4">
                        <p className="text-prp-light">{submission.customerName || 'N/A'}</p>
                      </td>
                      <td className="py-4">
                        <p className="text-prp-light font-medium">{formatCurrency(submission.totalCarValue)}</p>
                      </td>
                      <td className="py-4">
                        <p className="text-prp-gold font-medium">{formatCurrency(submission.prpSales)}</p>
                      </td>
                      <td className="py-4">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                          submission.status === 'approved'
                            ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                            : submission.status === 'rejected'
                            ? 'bg-red-500/20 text-red-400 border border-red-500/30'
                            : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'
                        }`}>
                          {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                        </span>
                      </td>
                      <td className="py-4">
                        <p className="text-prp-silver text-sm">{formatDate(submission.createdAt)}</p>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="w-16 h-16 text-prp-silver/50 mx-auto mb-4" />
              <p className="text-prp-silver">No submissions found</p>
              <p className="text-prp-silver/70 text-sm mt-1">Start by submitting your first form</p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center space-x-6">
          <Button
            onClick={() => router.push('/dashboard')}
            variant="primary"
            className="text-lg py-4 px-8"
          >
            Back to Dashboard
          </Button>
          <Button
            onClick={() => router.push('/forms')}
            variant="outline"
            className="text-lg py-4 px-8"
          >
            Submit New Form
          </Button>
          <Button
            onClick={fetchStats}
            variant="outline"
            className="text-lg py-4 px-8"
          >
            Refresh Stats
          </Button>
        </div>
      </main>
    </div>
  );
}
