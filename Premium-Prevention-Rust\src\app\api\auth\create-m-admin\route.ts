import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { hashPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const adminEmail = 'mum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com';
    const adminPassword = '132Vandijk@!';

    // Check if user already exists
    const existingUser = await User.findOne({ email: adminEmail });
    if (existingUser) {
      // If user exists but is not admin, update their role
      if (existingUser.role !== 'admin') {
        // Hash the admin password
        const hashedPassword = await hashPassword(adminPassword);

        // Update user to admin
        const updatedUser = await User.findByIdAndUpdate(
          existingUser._id,
          {
            role: 'admin',
            password: hashedPassword,
            isActive: true
          },
          { new: true }
        );

        return NextResponse.json(
          {
            message: 'User updated to admin successfully',
            admin: {
              id: updatedUser._id,
              name: updatedUser.name,
              email: updatedUser.email,
              role: updatedUser.role,
              isActive: updatedUser.isActive
            }
          },
          { status: 200 }
        );
      } else {
        return NextResponse.json(
          {
            message: 'Admin user with m already exists',
            admin: {
              id: existingUser._id,
              name: existingUser.name,
              email: existingUser.email,
              role: existingUser.role,
              isActive: existingUser.isActive
            }
          },
          { status: 200 }
        );
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(adminPassword);

    // Create admin user
    const adminUser = new User({
      name: 'Admin',
      email: adminEmail,
      password: hashedPassword,
      phone: '',
      dealership: 'Premium Rust Prevention HQ',
      role: 'admin',
      isActive: true
    });

    await adminUser.save();

    return NextResponse.json(
      { 
        message: 'Admin user with m created successfully',
        admin: {
          id: adminUser._id,
          name: adminUser.name,
          email: adminUser.email,
          role: adminUser.role
        }
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Admin creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}
