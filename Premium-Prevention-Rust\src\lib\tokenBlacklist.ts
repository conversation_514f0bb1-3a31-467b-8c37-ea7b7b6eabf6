// Simple in-memory token blacklist
// In production, this should be stored in Redis or database
const blacklistedTokens = new Set<string>();

export function addToBlacklist(token: string): void {
  blacklistedTokens.add(token);
}

export function isTokenBlacklisted(token: string): boolean {
  return blacklistedTokens.has(token);
}

export function clearBlacklist(): void {
  blacklistedTokens.clear();
}

// Clean up expired tokens periodically (optional)
export function cleanupExpiredTokens(): void {
  // This would require parsing JWT tokens to check expiration
  // For now, we'll keep it simple
  // In production, use a proper cache with TTL
}
