'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { User, getUserData, getAuthToken, clearAuthData, setAuthData } from '@/utils/auth';

// Global state for user data to sync across components
let globalUser: User | null = null;
const globalUserListeners: Set<(user: User | null) => void> = new Set();

export const useAuth = () => {
  const [user, setUser] = useState<User | null>(globalUser);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Subscribe to global user data changes
  useEffect(() => {
    const listener = (userData: User | null) => {
      setUser(userData);
    };

    globalUserListeners.add(listener);

    return () => {
      globalUserListeners.delete(listener);
    };
  }, []);

  useEffect(() => {
    const token = getAuthToken();
    const userData = getUserData();

    if (token && userData) {
      // Update global state
      globalUser = userData;
      // Notify all listeners
      globalUserListeners.forEach(listener => listener(userData));
    }

    setIsLoading(false);
  }, []);

  const logout = async () => {
    try {
      const response = await fetch('/api/auth/logout', { method: 'POST' });
      const result = await response.json();

      // Clear all client-side authentication data
      clearAuthData();

      // Also clear any other possible localStorage items
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('authToken');
        localStorage.removeItem('userData');

        // Clear sessionStorage as well
        sessionStorage.removeItem('auth_token');
        sessionStorage.removeItem('user_data');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('user');
      }

      // Update global state
      globalUser = null;
      // Notify all listeners
      globalUserListeners.forEach(listener => listener(null));

      // Redirect to home page instead of login page
      router.push('/');

    } catch (error) {
      console.error('Logout error:', error);

      // Even if API call fails, clear local data and redirect
      clearAuthData();

      if (typeof window !== 'undefined') {
        localStorage.clear(); // Clear all localStorage as fallback
        sessionStorage.clear(); // Clear all sessionStorage as fallback
      }

      globalUser = null;
      globalUserListeners.forEach(listener => listener(null));

      router.push('/');
    }
  };

  const updateUser = useCallback((userData: User) => {
    // Update localStorage with new user data
    const token = getAuthToken();
    if (token) {
      setAuthData(token, userData);
    }

    // Update global state
    globalUser = userData;
    // Notify all listeners
    globalUserListeners.forEach(listener => listener(userData));
  }, []);

  return {
    user,
    isLoading,
    isAuthenticated: !!user,
    logout,
    updateUser
  };
};
