import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { hashPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const adminEmails = ['umer<PERSON><PERSON><PERSON><PERSON><EMAIL>', '<EMAIL>'];
    const adminPassword = '132Vandijk@!';

    // Hash password
    const hashedPassword = await hashPassword(adminPassword);

    const createdAdmins = [];

    // Create admin users for both email variations
    for (const adminEmail of adminEmails) {
      // Check if admin already exists
      const existingAdmin = await User.findOne({ email: adminEmail });
      if (!existingAdmin) {
        // Create admin user
        const adminUser = new User({
          name: 'Admin',
          email: adminEmail,
          password: hashedPassword,
          phone: '',
          dealership: 'Premium Rust Prevention HQ',
          role: 'admin',
          isActive: true
        });

        await adminUser.save();
        createdAdmins.push({
          id: adminUser._id,
          name: adminUser.name,
          email: adminUser.email,
          role: adminUser.role
        });
      }
    }

    if (createdAdmins.length > 0) {
      return NextResponse.json(
        {
          message: `${createdAdmins.length} admin user(s) created successfully`,
          admins: createdAdmins
        },
        { status: 201 }
      );
    } else {
      return NextResponse.json(
        { message: 'Admin users already exist' },
        { status: 200 }
      );
    }

  } catch (error) {
    console.error('Admin creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create admin user' },
      { status: 500 }
    );
  }
}
