'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import Logo from '@/components/Logo';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Shield, Lock, Mail, Eye, EyeOff } from 'lucide-react';

const adminLoginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(1, 'Password is required'),
});

type AdminLoginFormData = z.infer<typeof adminLoginSchema>;

export default function AdminLoginPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AdminLoginFormData>({
    resolver: zodResolver(adminLoginSchema),
  });

  useEffect(() => {
    // Check if admin is already logged in
    const token = localStorage.getItem('admin_token');
    const adminData = localStorage.getItem('admin_data');
    if (token && adminData) {
      try {
        const admin = JSON.parse(adminData);
        if (admin.role === 'admin') {
          router.push('/admin/dashboard');
        }
      } catch (error) {
        localStorage.removeItem('admin_token');
        localStorage.removeItem('admin_data');
      }
    }
  }, [router]);

  const onSubmit = async (data: AdminLoginFormData) => {
    setIsLoading(true);
    setError('');

    console.log('Form submitted with:', data);

    // Allow authorized admin emails
    const authorizedAdminEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ];

    if (!authorizedAdminEmails.includes(data.email.toLowerCase())) {
      setError('Access denied. Invalid admin credentials.');
      setIsLoading(false);
      return;
    }

    try {
      console.log('Sending request to admin-login API...');
      const response = await fetch('/api/auth/admin-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      console.log('Response status:', response.status);
      const result = await response.json();
      console.log('Response data:', result);

      if (!response.ok) {
        throw new Error(result.error || 'Login failed');
      }

      // Store admin token and data
      localStorage.setItem('admin_token', result.token);
      localStorage.setItem('admin_data', JSON.stringify(result.admin));

      console.log('Login successful, redirecting to dashboard...');
      // Redirect to admin dashboard
      router.push('/admin/dashboard');
    } catch (err) {
      console.error('Login error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred during login');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-prp-dark via-gray-900 to-prp-dark flex items-center justify-center px-4 sm:px-6 lg:px-8 py-12">
      <div className="max-w-lg w-full space-y-8">
        {/* Logo and Header */}
        <div className="text-center">
          <div className="flex justify-center mb-8">
            <Logo size="lg" showText={true} />
          </div>
          <div className="flex items-center justify-center mb-4">
            <Shield className="w-8 h-8 text-prp-gold mr-3" />
            <h2 className="text-4xl font-bold text-prp-light">Admin Portal</h2>
          </div>
          <p className="text-lg text-prp-silver">Secure access to Premium Rust Prevention administration</p>
        </div>

        {/* Login Form */}
        <div className="bg-prp-dark/60 border border-prp-gold/30 rounded-2xl p-8 shadow-2xl">
          {error && (
            <div className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-xl">
              <p className="text-red-400 text-sm font-medium">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-prp-light mb-2">
                Admin Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver" />
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter admin email"
                  className="pl-10"
                  {...register('email')}
                  error={errors.email?.message}
                />
              </div>
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-prp-light mb-2">
                Admin Password
              </label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-prp-silver" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter admin password"
                  className="pl-10 pr-12"
                  {...register('password')}
                  error={errors.password?.message}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-prp-silver hover:text-prp-gold transition-colors duration-200"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              variant="primary"
              size="lg"
              loading={isLoading}
              className="w-full"
            >
              Access Admin Panel
            </Button>
          </form>

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-prp-gold/10 border border-prp-gold/30 rounded-xl">
            <div className="flex items-center">
              <Shield className="w-5 h-5 text-prp-gold mr-2" />
              <p className="text-prp-gold text-sm font-medium">
                Secure Admin Access Only
              </p>
            </div>
            <p className="text-prp-silver text-xs mt-1">
              This portal is restricted to authorized administrators only.
            </p>
          </div>
        </div>

        {/* Back to Main Site */}
        <div className="text-center">
          <button
            onClick={() => router.push('/')}
            className="text-prp-silver hover:text-prp-gold transition-colors duration-200 text-sm"
          >
            ← Back to Main Site
          </button>
        </div>
      </div>
    </div>
  );
}
