/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import FormSubmission from '@/models/FormSubmission';
import User from '@/models/User';
import { verifyToken } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    // Verify token and check if user is admin
    const payload = verifyToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const skip = (page - 1) * limit;

    // Build query
    const query: any = {};
    if (status && ['pending', 'approved', 'rejected'].includes(status)) {
      query.status = status;
    }

    // Apply search filter to the database query (independent search)
    if (search) {
      const searchLower = search.toLowerCase();
      query.vehicleName = { $regex: searchLower, $options: 'i' };
    }

    // Get submissions with pagination and populate user data
    const submissions = await FormSubmission.find(query)
      .populate('userId', 'name email dealership phone')
      .sort({ [sortBy]: sortOrder === 'desc' ? -1 : 1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count with the same query (including search filter)
    const total = await FormSubmission.countDocuments(query);

    // Format submissions for admin view
    const formattedSubmissions = submissions.map(sub => ({
      id: sub._id,
      vehicleName: sub.vehicleName,
      vehicleId: sub.vehicleId,
      vehicleModel: sub.vehicleModel,
      vehicleYear: sub.vehicleYear,
      vehicleColor: sub.vehicleColor,
      totalCarValue: sub.totalCarValue,
      prpSales: sub.prpSales,
      customerName: sub.customerName,
      customerPhone: sub.customerPhone,
      customerEmail: sub.customerEmail,
      mileage: sub.mileage,
      condition: sub.condition,
      notes: sub.notes,
      status: sub.status,
      pictures: sub.pictures,
      date: sub.date,
      createdAt: sub.createdAt,
      updatedAt: sub.updatedAt,
      salesman: {
        id: sub.userId?._id,
        name: sub.userId?.name,
        email: sub.userId?.email,
        dealership: sub.userId?.dealership,
        phone: sub.userId?.phone
      }
    }));

    return NextResponse.json({
      submissions: formattedSubmissions,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Admin forms API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    await connectDB();

    // Get token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - No token provided' },
        { status: 401 }
      );
    }

    // Verify token and check if user is admin
    const payload = verifyToken(token);
    if (!payload || payload.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized - Admin access required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { submissionId, status, notes } = body;

    if (!submissionId || !status) {
      return NextResponse.json(
        { error: 'Submission ID and status are required' },
        { status: 400 }
      );
    }

    if (!['pending', 'approved', 'rejected'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be pending, approved, or rejected' },
        { status: 400 }
      );
    }

    // Update submission status
    const updateData: any = { status };
    if (notes) {
      updateData.adminNotes = notes;
    }

    const updatedSubmission = await FormSubmission.findByIdAndUpdate(
      submissionId,
      updateData,
      { new: true }
    ).populate('userId', 'name email dealership');

    if (!updatedSubmission) {
      return NextResponse.json(
        { error: 'Submission not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Submission status updated successfully',
      submission: updatedSubmission
    });

  } catch (error) {
    console.error('Admin forms update API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
