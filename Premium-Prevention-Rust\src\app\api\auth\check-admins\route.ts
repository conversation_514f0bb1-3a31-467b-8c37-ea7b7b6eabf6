import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';

export async function GET(request: NextRequest) {
  try {
    await connectDB();

    // Get all admin users
    const admins = await User.find({ role: 'admin' }).select('-password');

    return NextResponse.json(
      { 
        message: 'Admin users found',
        admins: admins
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Check admins error:', error);
    return NextResponse.json(
      { error: 'Failed to check admin users' },
      { status: 500 }
    );
  }
}
