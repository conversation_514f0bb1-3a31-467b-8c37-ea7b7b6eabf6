import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import User from '@/models/User';
import { verifyPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { email, password } = body;

    console.log('Testing login with:', { email, password });

    // Find admin user by email
    const admin = await User.findOne({ 
      email: email.toLowerCase(),
      role: 'admin'
    });

    console.log('Found admin:', admin ? { id: admin._id, email: admin.email, role: admin.role } : 'Not found');

    if (!admin) {
      return NextResponse.json(
        { error: 'Admin not found', email: email.toLowerCase() },
        { status: 404 }
      );
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, admin.password);
    console.log('Password valid:', isPasswordValid);

    return NextResponse.json(
      { 
        message: 'Test completed',
        adminFound: true,
        passwordValid: isPasswordValid,
        adminEmail: admin.email
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Test admin login error:', error);
    return NextResponse.json(
      { error: 'Test failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
