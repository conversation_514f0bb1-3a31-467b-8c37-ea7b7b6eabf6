'use client';

import { useState, useEffect, useCallback } from 'react';

// Global state for profile image to sync across components
let globalProfileImage: string | null = null;
const globalListeners: Set<(image: string | null) => void> = new Set();

export const useProfileImage = () => {
  const [profileImage, setProfileImage] = useState<string | null>(globalProfileImage);
  const [isLoading, setIsLoading] = useState(false);

  // Subscribe to global profile image changes
  useEffect(() => {
    const listener = (image: string | null) => {
      setProfileImage(image);
    };
    
    globalListeners.add(listener);
    
    return () => {
      globalListeners.delete(listener);
    };
  }, []);

  const loadProfileImage = useCallback(async () => {
    setIsLoading(true);
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/user/profile-image', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        const imageUrl = data.profileImage;
        
        // Update global state
        globalProfileImage = imageUrl;
        
        // Notify all listeners
        globalListeners.forEach(listener => listener(imageUrl));
      }
    } catch (error) {
      console.error('Failed to load profile image:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateProfileImage = useCallback((imageUrl: string | null) => {
    // Update global state
    globalProfileImage = imageUrl;
    
    // Notify all listeners
    globalListeners.forEach(listener => listener(imageUrl));
  }, []);

  return {
    profileImage,
    isLoading,
    loadProfileImage,
    updateProfileImage
  };
};
