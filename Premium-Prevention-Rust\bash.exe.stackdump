Stack trace:
Frame         Function      Args
0007FFFFA770  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA770, 0007FFFF9670) msys-2.0.dll+0x1FE8E
0007FFFFA770  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAA48) msys-2.0.dll+0x67F9
0007FFFFA770  000210046832 (000210286019, 0007FFFFA628, 0007FFFFA770, 000000000000) msys-2.0.dll+0x6832
0007FFFFA770  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA770  000210068E24 (0007FFFFA780, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAA50  00021006A225 (0007FFFFA780, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8F7B20000 ntdll.dll
7FF8F5D30000 KERNEL32.DLL
7FF8F52C0000 KERNELBASE.dll
7FF8F7360000 USER32.dll
7FF8F5290000 win32u.dll
7FF8F5E00000 GDI32.dll
7FF8F4FD0000 gdi32full.dll
7FF8F5750000 msvcp_win.dll
7FF8F4E80000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8F5F40000 advapi32.dll
7FF8F79B0000 msvcrt.dll
7FF8F5A90000 sechost.dll
7FF8F6000000 RPCRT4.dll
7FF8F4270000 CRYPTBASE.DLL
7FF8F56B0000 bcryptPrimitives.dll
7FF8F7970000 IMM32.DLL
