import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { verifyToken } from '@/lib/auth';
import fs from 'fs';
import path from 'path';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;



  // Removed automatic redirect logic to prevent auto-login after logout
  // Users should be able to access login page even if they have tokens
  // This prevents the issue where users are automatically logged back in after logout

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
